<?php

namespace App\Http\Controllers;

use App\Models\LandAcquisition;
use App\Models\LandOwner;
use App\Models\LandAddress;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LandAcquisitionController extends Controller
{
    /**
     * Constructor to apply auth middleware
     */
    public function __construct()
    {
        // Temporarily disable auth for debugging
        // $this->middleware('auth:sanctum');
    }

    /**
     * Display a listing of land acquisitions
     */
    public function index(Request $request): JsonResponse
    {
        $query = LandAcquisition::with(['landOwner', 'landAddress.country', 'landAddress.state', 'landAddress.city']);

        // Apply filters
        if ($request->has('mauza') && $request->mauza !== '') {
            $query->byMauza($request->mauza);
        }

        if ($request->has('khatian') && $request->khatian !== '') {
            $query->byKhatian($request->khatian);
        }

        if ($request->has('record_dag') && $request->record_dag !== '') {
            $query->byRecordDag($request->record_dag);
        }

        if ($request->has('min_price') && $request->has('max_price')) {
            $query->byPriceRange($request->min_price, $request->max_price);
        }

        if ($request->has('min_land_size') && $request->has('max_land_size')) {
            $query->byLandSizeRange($request->min_land_size, $request->max_land_size);
        }

        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('record_dag', 'like', "%{$search}%")
                  ->orWhere('khatian', 'like', "%{$search}%")
                  ->orWhere('mauza', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $landAcquisitions = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $landAcquisitions,
            'statistics' => LandAcquisition::getStatistics()
        ]);
    }

    /**
     * Store a newly created land acquisition
     */
    public function store(Request $request): JsonResponse
    {
        // Validate land acquisition fields
        $rules = [
            'record_dag' => 'required|string|max:255|unique:land_acquisitions,record_dag',
            'khatian' => 'required|string|max:255',
            'mauza' => 'required|string|max:255',
            'land_size' => 'required|numeric|min:0',
            'acquisition_price' => 'required|numeric|min:0',

            // For existing land owner
            'landOwners_id' => 'nullable|integer|exists:land_owners,id',

            // For new land owner (conditional validation will be done below)
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'father_name' => 'nullable|string|max:255',
            'mother_name' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'nid_number' => 'nullable|string|max:50',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            
            // Document upload fields - only validate as image if actually files
            'document_type' => 'nullable|string|in:nid,passport',

            // Land address fields - simplified structure
            'land_address.country_id' => 'nullable|integer|exists:countries,id',
            'land_address.state_id' => 'nullable|integer|exists:states,id',
            'land_address.city_id' => 'nullable|integer|exists:cities,id',
            'land_address.specific_address' => 'nullable|string|max:500',
        ];

        // Handle photo validation conditionally - allow all common image formats
        if ($request->hasFile('photo')) {
            $rules['photo'] = 'nullable|image|mimes:jpeg,png,jpg,gif,webp,bmp,tiff,svg|max:10240'; // Increased to 10MB, added more formats
        }
        // Note: Removed strict validation for non-file photo values to be more permissive

        // Handle document validation conditionally
        if ($request->hasFile('nid_front')) {
            $rules['nid_front'] = 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120';
        }
        if ($request->hasFile('nid_back')) {
            $rules['nid_back'] = 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120';
        }
        if ($request->hasFile('passport_photo')) {
            $rules['passport_photo'] = 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120';
        }

        $validated = $request->validate($rules);

        try {
            // Start database transaction
            DB::beginTransaction();

            $landOwnerId = null;

            // Check if using existing land owner or creating new one
            $landOwnersId = $request->input('landOwners_id');
            
            if (!empty($landOwnersId) && $landOwnersId !== '' && $landOwnersId !== null) {
                // Use existing land owner
                $landOwnerId = $landOwnersId;
            } else {
                // Create new land owner - validate required fields
                if (empty($validated['first_name']) || empty($validated['last_name']) || empty($validated['father_name']) || empty($validated['address'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'First name, last name, father name, and address are required for new land owner'
                    ], 422);
                }

                // Handle photo upload if present
                $photoUrl = null;
                if ($request->hasFile('photo')) {
                    try {
                        $photoUrl = $this->handleImageUpload($request->file('photo'));
                    } catch (\Exception $e) {
                        DB::rollback();
                        return response()->json([
                            'success' => false,
                            'message' => 'Photo upload failed: ' . $e->getMessage()
                        ], 500);
                    }
                }

                // Handle document uploads
                $nidFrontUrl = null;
                $nidBackUrl = null;
                $passportPhotoUrl = null;

                if ($request->hasFile('nid_front')) {
                    try {
                        $nidFrontUrl = $this->handleDocumentUpload($request->file('nid_front'), 'nid_front');
                    } catch (\Exception $e) {
                        DB::rollback();
                        return response()->json([
                            'success' => false,
                            'message' => 'NID front upload failed: ' . $e->getMessage()
                        ], 500);
                    }
                }

                if ($request->hasFile('nid_back')) {
                    try {
                        $nidBackUrl = $this->handleDocumentUpload($request->file('nid_back'), 'nid_back');
                    } catch (\Exception $e) {
                        DB::rollback();
                        return response()->json([
                            'success' => false,
                            'message' => 'NID back upload failed: ' . $e->getMessage()
                        ], 500);
                    }
                }

                if ($request->hasFile('passport_photo')) {
                    try {
                        $passportPhotoUrl = $this->handleDocumentUpload($request->file('passport_photo'), 'passport');
                    } catch (\Exception $e) {
                        DB::rollback();
                        return response()->json([
                            'success' => false,
                            'message' => 'Passport photo upload failed: ' . $e->getMessage()
                        ], 500);
                    }
                }

                $landOwner = LandOwner::create([
                    'first_name' => $validated['first_name'],
                    'last_name' => $validated['last_name'],
                    'father_name' => $validated['father_name'],
                    'mother_name' => $validated['mother_name'],
                    'phone' => $validated['phone'],
                    'nid_number' => $validated['nid_number'],
                    'email' => $validated['email'],
                    'address' => $validated['address'],
                    'photo' => $photoUrl,
                    'document_type' => $validated['document_type'] ?? 'nid',
                    'nid_front' => $nidFrontUrl,
                    'nid_back' => $nidBackUrl,
                    'passport_photo' => $passportPhotoUrl,
                ]);

                $landOwnerId = $landOwner->id;
            }

            // Create land acquisition
            $landAcquisition = LandAcquisition::create([
                'record_dag' => $validated['record_dag'],
                'khatian' => $validated['khatian'],
                'mauza' => $validated['mauza'],
                'land_size' => $validated['land_size'],
                'acquisition_price' => $validated['acquisition_price'],
                'landOwners_id' => $landOwnerId,
            ]);

            // Create land address if provided
            if ($request->has('land_address')) {
                $landAddressData = $request->input('land_address');
                
                // Handle both array format and individual field format
                if (is_array($landAddressData)) {
                    // Filter out empty values
                    $landAddressData = array_filter($landAddressData, function($value) {
                        return $value !== null && $value !== '';
                    });
                    
                    // Only create land address if we have some data
                    if (!empty($landAddressData)) {
                        $landAddressData['land_acquisition_id'] = $landAcquisition->id;
                        
                        LandAddress::create($landAddressData);
                    }
                } else {
                    // Check for individual land address fields (FormData format)
                    $landAddressFields = [
                        'country_id' => $request->input('land_address.country_id'),
                        'state_id' => $request->input('land_address.state_id'),
                        'city_id' => $request->input('land_address.city_id'),
                        'specific_address' => $request->input('land_address.specific_address'),
                    ];
                    
                    // Filter out empty values
                    $landAddressFields = array_filter($landAddressFields, function($value) {
                        return $value !== null && $value !== '';
                    });
                    
                    // Only create land address if we have some data
                    if (!empty($landAddressFields)) {
                        $landAddressFields['land_acquisition_id'] = $landAcquisition->id;
                        
                        LandAddress::create($landAddressFields);
                    }
                }
            } else {
                // Check for individual land address fields (FormData format)
                $landAddressFields = [
                    'country_id' => $request->input('land_address.country_id'),
                    'state_id' => $request->input('land_address.state_id'),
                    'city_id' => $request->input('land_address.city_id'),
                    'specific_address' => $request->input('land_address.specific_address'),
                ];
                
                // Filter out empty values
                $landAddressFields = array_filter($landAddressFields, function($value) {
                    return $value !== null && $value !== '';
                });
                
                // Only create land address if we have some data
                if (!empty($landAddressFields)) {
                    $landAddressFields['land_acquisition_id'] = $landAcquisition->id;
                    
                    LandAddress::create($landAddressFields);
                }
            }

            // Load the relationships
            $landAcquisition->load(['landOwner', 'landAddress']);

            // Commit the transaction
            DB::commit();

            $message = $landOwnersId
                ? 'Land acquisition created with existing owner successfully'
                : 'Land acquisition and new owner created successfully';

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $landAcquisition
            ], 201);

        } catch (\Exception $e) {
            // Rollback the transaction on error
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error creating land acquisition: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified land acquisition
     */
    public function show(LandAcquisition $landAcquisition): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $landAcquisition->load(['landOwner', 'landAddress.country', 'landAddress.state', 'landAddress.city'])
        ]);
    }

    /**
     * Update the specified land acquisition
     */
    public function update(Request $request, LandAcquisition $landAcquisition): JsonResponse
    {
        // Validate both land acquisition and land owner data
        $rules = [
            // Land acquisition fields
            'record_dag' => 'sometimes|required|string|max:255|unique:land_acquisitions,record_dag,' . $landAcquisition->id,
            'khatian' => 'sometimes|required|string|max:255',
            'mauza' => 'sometimes|required|string|max:255',
            'land_size' => 'sometimes|required|numeric|min:0',
            'acquisition_price' => 'sometimes|required|numeric|min:0',

            // Land owner fields
            'first_name' => 'sometimes|required|string|max:255',
            'last_name' => 'sometimes|required|string|max:255',
            'father_name' => 'sometimes|required|string|max:255',
            'mother_name' => 'sometimes|nullable|string|max:255',
            'phone' => 'sometimes|nullable|string|max:20',
            'email' => 'sometimes|nullable|email|max:255',
            'address' => 'sometimes|required|string',

            // Land address fields - simplified structure
            'land_address.country_id' => 'sometimes|nullable|integer|exists:countries,id',
            'land_address.state_id' => 'sometimes|nullable|integer|exists:states,id',
            'land_address.city_id' => 'sometimes|nullable|integer|exists:cities,id',
            'land_address.specific_address' => 'sometimes|nullable|string|max:500',
        ];

        // Handle photo validation conditionally
        if ($request->hasFile('photo')) {
            $rules['photo'] = 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120';
        } elseif ($request->has('photo')) {
            // If photo field exists but is not a file, it should be null, empty string, or existing URL
            $photoValue = $request->input('photo');
            if ($photoValue !== null && $photoValue !== '' && !is_string($photoValue)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid photo field format'
                ], 422);
            }
        }

        $validated = $request->validate($rules);

        try {
            // Start database transaction
            DB::beginTransaction();

            // Update land owner if land owner fields are provided
            if (isset($validated['first_name']) || isset($validated['last_name']) || isset($validated['father_name']) || isset($validated['address'])) {
                $landOwner = $landAcquisition->landOwner;
                if ($landOwner) {
                    $landOwnerData = [];
                    if (isset($validated['first_name'])) $landOwnerData['first_name'] = $validated['first_name'];
                    if (isset($validated['last_name'])) $landOwnerData['last_name'] = $validated['last_name'];
                    if (isset($validated['father_name'])) $landOwnerData['father_name'] = $validated['father_name'];
                    if (isset($validated['mother_name'])) $landOwnerData['mother_name'] = $validated['mother_name'];
                    if (isset($validated['phone'])) $landOwnerData['phone'] = $validated['phone'];
                    if (isset($validated['email'])) $landOwnerData['email'] = $validated['email'];
                    if (isset($validated['address'])) $landOwnerData['address'] = $validated['address'];

                    // Handle photo upload if present
                    if ($request->hasFile('photo')) {
                        // Delete old photo if exists
                        if ($landOwner->photo) {
                            $this->deleteOldImage($landOwner->photo);
                        }
                        
                        try {
                            $photoUrl = $this->handleImageUpload($request->file('photo'));
                            $landOwnerData['photo'] = $photoUrl;
                        } catch (\Exception $e) {
                            DB::rollback();
                            return response()->json([
                                'success' => false,
                                'message' => 'Photo upload failed: ' . $e->getMessage()
                            ], 500);
                        }
                    }

                    $landOwner->update($landOwnerData);
                }
            }

            // Update land acquisition
            $landAcquisitionData = [];
            if (isset($validated['record_dag'])) $landAcquisitionData['record_dag'] = $validated['record_dag'];
            if (isset($validated['khatian'])) $landAcquisitionData['khatian'] = $validated['khatian'];
            if (isset($validated['mauza'])) $landAcquisitionData['mauza'] = $validated['mauza'];
            if (isset($validated['land_size'])) $landAcquisitionData['land_size'] = $validated['land_size'];
            if (isset($validated['acquisition_price'])) $landAcquisitionData['acquisition_price'] = $validated['acquisition_price'];

            $landAcquisition->update($landAcquisitionData);

            // Update or create land address if provided
            if ($request->has('land_address')) {
                $landAddressData = $request->input('land_address');
                
                // Handle both array format and individual field format
                if (is_array($landAddressData)) {
                    // Filter out empty values
                    $landAddressData = array_filter($landAddressData, function($value) {
                        return $value !== null && $value !== '';
                    });
                    
                    if (!empty($landAddressData)) {
                        // Update or create land address
                        $landAcquisition->landAddress()->updateOrCreate(
                            ['land_acquisition_id' => $landAcquisition->id],
                            $landAddressData
                        );
                    } else {
                        // If empty land address data is sent, delete existing address
                        $landAcquisition->landAddress()->delete();
                    }
                } else {
                    // Check for individual land address fields (FormData format)
                    $landAddressFields = [
                        'country_id' => $request->input('land_address.country_id'),
                        'state_id' => $request->input('land_address.state_id'),
                        'city_id' => $request->input('land_address.city_id'),
                        'specific_address' => $request->input('land_address.specific_address'),
                    ];
                    
                    // Filter out empty values
                    $landAddressFields = array_filter($landAddressFields, function($value) {
                        return $value !== null && $value !== '';
                    });
                    
                    if (!empty($landAddressFields)) {
                        // Update or create land address
                        $landAcquisition->landAddress()->updateOrCreate(
                            ['land_acquisition_id' => $landAcquisition->id],
                            $landAddressFields
                        );
                    } else {
                        // If no land address data, delete existing address
                        $landAcquisition->landAddress()->delete();
                    }
                }
            } else {
                // Check for individual land address fields (FormData format)
                $landAddressFields = [
                    'country_id' => $request->input('land_address.country_id'),
                    'state_id' => $request->input('land_address.state_id'),
                    'city_id' => $request->input('land_address.city_id'),
                    'specific_address' => $request->input('land_address.specific_address'),
                ];
                
                // Filter out empty values
                $landAddressFields = array_filter($landAddressFields, function($value) {
                    return $value !== null && $value !== '';
                });
                
                if (!empty($landAddressFields)) {
                    // Update or create land address
                    $landAcquisition->landAddress()->updateOrCreate(
                        ['land_acquisition_id' => $landAcquisition->id],
                        $landAddressFields
                    );
                } else {
                    // If no land address data, delete existing address
                    $landAcquisition->landAddress()->delete();
                }
            }

            // Commit the transaction
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Land acquisition updated successfully',
                'data' => $landAcquisition->load(['landOwner', 'landAddress'])
            ]);

        } catch (\Exception $e) {
            // Rollback the transaction on error
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error updating land acquisition: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified land acquisition
     */
    public function destroy(LandAcquisition $landAcquisition): JsonResponse
    {
        $landAcquisition->delete();

        return response()->json([
            'success' => true,
            'message' => 'Land acquisition deleted successfully'
        ]);
    }

    /**
     * Get dashboard statistics
     */
    public function statistics(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => LandAcquisition::getStatistics()
        ]);
    }

    /**
     * Handle image upload for land owner photos
     */
    private function handleImageUpload($file): string
    {
        try {
            \Log::info('🔄 Starting image upload process (Land Acquisition)', [
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'temp_path' => $file->getPathname(),
                'is_valid' => $file->isValid()
            ]);

            // Check if file is valid
            if (!$file->isValid()) {
                throw new \Exception('Uploaded file is not valid');
            }

            // Generate unique filename
            $filename = 'landowner_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            
            \Log::info('📁 Generated filename', ['filename' => $filename]);
            
            // Create public/landowners directory if it doesn't exist
            $publicDir = public_path('landowners/photo');
            if (!is_dir($publicDir)) {
                \Log::info('📁 Creating public directory', ['dir' => $publicDir]);
                mkdir($publicDir, 0755, true);
            }
            
            // Move the uploaded file to public/landowners directory
            $destinationPath = $publicDir . DIRECTORY_SEPARATOR . $filename;
            
            if (move_uploaded_file($file->getPathname(), $destinationPath)) {
                \Log::info('📁 File moved to public directory', [
                    'destination' => $destinationPath,
                    'public_path' => '/landowners/photo/' . $filename
                ]);
                
                // Return the public URL path
                $url = '/landowners/photo/' . $filename;
                
                // Verify the file exists
                if (file_exists($destinationPath)) {
                    $fileSize = filesize($destinationPath);
                    \Log::info('✅ Upload verification passed', [
                        'file_path' => $destinationPath,
                        'public_url' => $url,
                        'file_size' => $fileSize
                    ]);
                } else {
                    \Log::error('❌ Upload verification failed - file not found');
                    throw new \Exception('File was not properly stored');
                }
                
                return $url;
            } else {
                throw new \Exception('Failed to move uploaded file to public directory');
            }
            
        } catch (\Exception $e) {
            \Log::error('💥 Image upload completely failed (Land Acquisition)', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file_info' => [
                    'name' => $file->getClientOriginalName() ?? 'unknown',
                    'size' => $file->getSize() ?? 0,
                    'error_code' => $file->getError() ?? 'none'
                ]
            ]);
            throw $e;
        }
    }

    /**
     * Delete old image file
     */
    private function deleteOldImage(string $imageUrl): void
    {
        try {
            // Extract the filename from URL
            // URL format: /landowners/photo/filename.jpg
            $filename = basename($imageUrl);
            
            // Construct the full file path with proper directory separators
            $filePath = public_path('landowners' . DIRECTORY_SEPARATOR . 'photo' . DIRECTORY_SEPARATOR . $filename);
            
            \Log::info('Attempting to delete image file (Land Acquisition)', [
                'image_url' => $imageUrl,
                'filename' => $filename,
                'file_path' => $filePath,
                'file_exists' => file_exists($filePath)
            ]);
            
            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    \Log::info('Old image deleted successfully (Land Acquisition)', ['file_path' => $filePath]);
                } else {
                    \Log::warning('Failed to delete image file (Land Acquisition)', ['file_path' => $filePath]);
                }
            } else {
                \Log::info('Old image file not found, skipping deletion (Land Acquisition)', ['file_path' => $filePath]);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to delete old image (Land Acquisition)', [
                'image_url' => $imageUrl,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    private function handleDocumentUpload($file, $documentType): string
    {
        try {
            \Log::info('🔄 Starting document upload process (Land Acquisition)', [
                'document_type' => $documentType,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'temp_path' => $file->getPathname(),
                'is_valid' => $file->isValid()
            ]);

            // Check if file is valid
            if (!$file->isValid()) {
                throw new \Exception('Uploaded document file is not valid');
            }

            // Generate unique filename with document type prefix
            $filename = 'landowner_' . $documentType . '_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            
            \Log::info('📁 Generated document filename', ['filename' => $filename]);
            
            // Create public/landowners/documents directory if it doesn't exist
            $publicDir = public_path('landowners/documents');
            if (!is_dir($publicDir)) {
                \Log::info('📁 Creating public documents directory', ['dir' => $publicDir]);
                mkdir($publicDir, 0755, true);
            }
            
            // Move the uploaded file to public/landowners/documents directory
            $destinationPath = $publicDir . DIRECTORY_SEPARATOR . $filename;
            
            if (move_uploaded_file($file->getPathname(), $destinationPath)) {
                \Log::info('📁 Document file moved to public directory', [
                    'destination' => $destinationPath,
                    'public_path' => '/landowners/documents/' . $filename
                ]);
                
                // Return the public URL path
                $url = '/landowners/documents/' . $filename;
                
                // Verify the file exists
                if (file_exists($destinationPath)) {
                    $fileSize = filesize($destinationPath);
                    \Log::info('✅ Document upload verification passed', [
                        'file_path' => $destinationPath,
                        'public_url' => $url,
                        'file_size' => $fileSize
                    ]);
                } else {
                    \Log::error('❌ Document upload verification failed - file not found');
                    throw new \Exception('Document file was not properly stored');
                }
                
                return $url;
            } else {
                throw new \Exception('Failed to move uploaded document file to public directory');
            }
            
        } catch (\Exception $e) {
            \Log::error('💥 Document upload completely failed (Land Acquisition)', [
                'document_type' => $documentType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
