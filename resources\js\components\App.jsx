import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './theme/ThemeProvider';
import { AuthProvider } from '../contexts/AuthContext';
import TranslationProvider from './TranslationProvider';
import DashboardLayout from './layout/DashboardLayout';
import DashboardOverview from './dashboard/DashboardOverview';
import AnalyticsPage from './dashboard/AnalyticsPage';
import SettingsPage from './dashboard/SettingsPage';
import OrdersPage from './dashboard/OrdersPage';
import ComponentsPage from './dashboard/ComponentsPage';
import LandOwnersPage from './dashboard/LandOwnersPage';
import LandAcquisitionPage from './dashboard/LandAcquisitionPage';
import RolePage from './dashboard/RolePage';
import CountryPage from './dashboard/CountryPage';
import StatePage from './dashboard/StatePage';
import AuthTestPage from './dashboard/AuthTestPage';
import PlaceholderPage from './dashboard/PlaceholderPage';
import PermissionRoute from './auth/PermissionRoute';
import LanguagePage from './dashboard/LanguagePage';
import CurrencyPage from './dashboard/CurrencyPage';

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('React Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', color: 'red', fontFamily: 'Arial' }}>
          <h1>Something went wrong!</h1>
          <p>Error: {this.state.error?.message}</p>
          <p>Check browser console for details.</p>
        </div>
      );
    }

    return this.props.children;
  }
}

export default function App() {
  console.log('App component rendering...');
  
  return (
    <ErrorBoundary>
      <AuthProvider>
        <TranslationProvider>
          <ThemeProvider defaultTheme="system" storageKey="dashboard-theme">
            <Router>
              <DashboardLayout>
              <Routes>
                {/* Default route redirects to dashboard */}
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                
                {/* Dashboard routes */}
                <Route path="/dashboard" element={<DashboardOverview />} />
                
                {/* Protected routes - require specific module access */}
                <Route 
                  path="/analytics" 
                  element={
                    <PermissionRoute requiredModule="analytics">
                      <AnalyticsPage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/land-owners" 
                  element={
                    <PermissionRoute requiredModule="land-owners">
                      <LandOwnersPage />
                    </PermissionRoute>
                  } 
                />
                
                <Route 
                  path="/lifecycle" 
                  element={
                    <PermissionRoute requiredModule="lifecycle">
                      <PlaceholderPage title="Lifecycle" description="Lifecycle management and tracking" />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/land-acquisition" 
                  element={
                    <PermissionRoute requiredModule="land-acquisition">
                      <LandAcquisitionPage />
                    </PermissionRoute>
                  } 
                />
                
                <Route 
                  path="/role" 
                  element={
                    <PermissionRoute requiredModule="role">
                      <RolePage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/country" 
                  element={
                    <PermissionRoute requiredModule="country">
                      <CountryPage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/state" 
                  element={
                    <PermissionRoute requiredModule="state">
                      <StatePage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/customers" 
                  element={
                    <PermissionRoute requiredModule="customers">
                      <PlaceholderPage title="Customers" description="Customer management functionality coming soon" />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/orders" 
                  element={
                    <PermissionRoute requiredModule="orders">
                      <OrdersPage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/components" 
                  element={
                    <PermissionRoute requiredModule="components">
                      <ComponentsPage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/reports" 
                  element={
                    <PermissionRoute requiredModule="reports">
                      <PlaceholderPage title="Reports" description="Generate and view detailed reports" />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/word-assistant" 
                  element={
                    <PermissionRoute requiredModule="word-assistant">
                      <PlaceholderPage title="Word Assistant" description="AI-powered document assistance" />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/more" 
                  element={
                    <PermissionRoute requiredModule="more">
                      <PlaceholderPage title="More" description="Additional features and tools" />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/settings" 
                  element={
                    <PermissionRoute requiredModule="settings">
                      <SettingsPage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/language" 
                  element={
                    <PermissionRoute requiredModule="language">
                      <LanguagePage />
                    </PermissionRoute>
                  } 
                />
                <Route 
                  path="/currency" 
                  element={
                    <PermissionRoute requiredModule="currency">
                      <CurrencyPage />
                    </PermissionRoute>
                  } 
                />
                
                {/* Development/Testing routes - always accessible for now */}
                <Route path="/auth-test" element={<AuthTestPage />} />
                
                {/* Catch-all route for 404 */}
                <Route 
                  path="*" 
                  element={<PlaceholderPage title="Page Not Found" description="The page you're looking for doesn't exist." />} 
                />
              </Routes>
            </DashboardLayout>
          </Router>
        </ThemeProvider>
        </TranslationProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}