import axios from 'axios';
import { API_BASE_URL, getValidatedApiUrl, getEnvironmentInfo } from '../config/api.js';

// Log environment info for debugging
console.log('🌍 Environment Info:', getEnvironmentInfo());

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 second timeout
});

// Store for dynamic API URL
let currentApiBaseUrl = API_BASE_URL;

// Initialize and test API connectivity
const initializeApi = async () => {
  try {
    console.log('🚀 Initializing API connection...');
    const validatedUrl = await getValidatedApiUrl();
    
    if (validatedUrl !== currentApiBaseUrl) {
      console.log('🔄 Updating API URL from', currentApiBaseUrl, 'to', validatedUrl);
      currentApiBaseUrl = validatedUrl;
      api.defaults.baseURL = validatedUrl;
    }
    
    console.log('✅ API initialized successfully with URL:', currentApiBaseUrl);
  } catch (error) {
    console.error('❌ API initialization failed:', error);
  }
};

// Initialize API on module load
initializeApi();

// Export function to manually test/reinitialize API
export const reinitializeApi = initializeApi;

// Export function to get current API info
export const getApiInfo = () => ({
  currentUrl: currentApiBaseUrl,
  originalUrl: API_BASE_URL,
  environmentInfo: getEnvironmentInfo()
});

// Add request interceptor for authentication and dynamic URL handling
api.interceptors.request.use(
  async (config) => {
    // Update base URL if it has changed
    if (currentApiBaseUrl !== API_BASE_URL) {
      config.baseURL = currentApiBaseUrl;
      console.log('🔄 Updated API Base URL to:', currentApiBaseUrl);
    }
    
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Only set Content-Type to application/json for non-FormData requests
    if (!(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json';
    }
    
    // Log request details for debugging
    console.log('📤 API Request:', {
      method: config.method.toUpperCase(),
      url: `${config.baseURL}${config.url}`,
      hasAuth: !!token,
      isFormData: config.data instanceof FormData
    });
    
    return config;
  },
  (error) => {
    console.error('❌ Request setup error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling and automatic URL switching
api.interceptors.response.use(
  (response) => {
    // Log successful response
    console.log('📥 API Response:', {
      status: response.status,
      url: response.config.url,
      method: response.config.method.toUpperCase()
    });
    return response;
  },
  async (error) => {
    console.error('❌ API Error:', {
      status: error.response?.status,
      message: error.message,
      url: error.config?.url,
      baseURL: error.config?.baseURL
    });
    
    // Handle authentication errors
    if (error.response?.status === 401) {
      console.warn('🔒 Authentication failed - clearing token');
      localStorage.removeItem('auth_token');
      window.location.reload();
      return Promise.reject(error);
    }
    
    // Handle connection errors - try to find a working API URL
    if (!error.response && (error.code === 'ECONNABORTED' || error.message.includes('Network Error'))) {
      console.warn('🌐 Network error detected, attempting to find working API URL...');
      
      try {
        const newApiUrl = await getValidatedApiUrl();
        if (newApiUrl !== currentApiBaseUrl) {
          console.log('🔄 Switching to working API URL:', newApiUrl);
          currentApiBaseUrl = newApiUrl;
          api.defaults.baseURL = newApiUrl;
          
          // Retry the original request with new URL
          const retryConfig = { ...error.config };
          retryConfig.baseURL = newApiUrl;
          console.log('🔁 Retrying request with new URL...');
          return api.request(retryConfig);
        }
      } catch (urlError) {
        console.error('❌ Failed to find working API URL:', urlError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Land Owner API functions

// Get all land owners with pagination and search
export const getAll = async (params = {}) => {
  try {
    const response = await api.get('/land-owners', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching land owners:', error);
    throw error;
  }
};

// Get single land owner by ID
export const getById = async (id) => {
  try {
    const response = await api.get(`/land-owners/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching land owner ${id}:`, error);
    throw error;
  }
};

// Create new land owner
export const create = async (data) => {
  try {
    console.log('🚀 Creating land owner with data:', {
      ...data,
      photo: data.photo instanceof File ? `File(${data.photo.name}, ${data.photo.size} bytes)` : data.photo,
      nid_front: data.nid_front instanceof File ? `File(${data.nid_front.name}, ${data.nid_front.size} bytes)` : data.nid_front,
      nid_back: data.nid_back instanceof File ? `File(${data.nid_back.name}, ${data.nid_back.size} bytes)` : data.nid_back,
      passport_photo: data.passport_photo instanceof File ? `File(${data.passport_photo.name}, ${data.passport_photo.size} bytes)` : data.passport_photo,
    });

    // Create FormData for file uploads
    const formData = new FormData();
    
    // Add all non-empty fields to FormData
    for (const key in data) {
      const value = data[key];
      
      // Skip null, undefined, or empty string values
      if (value !== null && value !== undefined && value !== '') {
        if (value instanceof File) {
          // For file uploads, add the file directly
          formData.append(key, value);
          console.log(`📁 Added file ${key}:`, value.name, `(${value.size} bytes)`);
        } else {
          // For regular fields, add as string
          formData.append(key, String(value));
          console.log(`📝 Added field ${key}:`, value);
        }
      }
    }

    // Log FormData contents for debugging
    console.log('📤 FormData contents:');
    for (let [key, value] of formData.entries()) {
      if (value instanceof File) {
        console.log(`  ${key}: File - ${value.name} (${value.size} bytes)`);
      } else {
        console.log(`  ${key}: ${value}`);
      }
    }

    // Don't set Content-Type header - let browser set it with proper boundary
    const response = await api.post('/land-owners', formData);
    
    console.log('✅ Land owner created successfully:', response.data);
    return response.data;
    
  } catch (error) {
    console.error('❌ Error creating land owner:', error);
    
    // Log detailed error information
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
      
      // Return the error response data for better error handling
      if (error.response.data) {
        return error.response.data;
      }
    } else if (error.request) {
      console.error('Request was made but no response received:', error.request);
    } else {
      console.error('Error setting up request:', error.message);
    }
    
    // Re-throw for handling in component
    throw error;
  }
};

// Update existing land owner
export const update = async (id, data) => {
  try {
    // Check if we have any file data that needs FormData
    const hasFiles = Object.keys(data).some(key => {
      const value = data[key];
      return value instanceof File;
    });

    console.log('🔄 Land Owner Update Request:', {
      id,
      hasFiles,
      fileFields: Object.keys(data).filter(key => data[key] instanceof File),
      data: {
        ...data,
        photo: data.photo instanceof File ? `File(${data.photo.name}, ${data.photo.size} bytes)` : data.photo,
        nid_front: data.nid_front instanceof File ? `File(${data.nid_front.name}, ${data.nid_front.size} bytes)` : data.nid_front,
        nid_back: data.nid_back instanceof File ? `File(${data.nid_back.name}, ${data.nid_back.size} bytes)` : data.nid_back,
        passport_photo: data.passport_photo instanceof File ? `File(${data.passport_photo.name}, ${data.passport_photo.size} bytes)` : data.passport_photo,
      }
    });

    let requestData;
    let headers = {};

    if (hasFiles) {
      console.log('📁 Using FormData for file upload(s)');
      // Use FormData for file uploads
      requestData = new FormData();

      // Add _method for Laravel PUT request simulation
      requestData.append('_method', 'PUT');

      // Append all form fields to FormData
      Object.keys(data).forEach(key => {
        const value = data[key];

        // Skip null, undefined, or empty string values
        if (value === null || value === undefined || value === '') {
          console.log(`⏭️ Skipping ${key}: empty value`);
          return;
        }

        // Handle file fields vs regular fields
        if (value instanceof File) {
          console.log(`📝 Appending ${key}: File(${value.name}, ${value.size} bytes)`);
          requestData.append(key, value);
        } else if (typeof value === 'string' && value.startsWith('/landowners/')) {
          // This is an existing file URL, skip it to preserve existing file
          console.log(`⏭️ Skipping ${key}: existing file URL (${value})`);
        } else {
          console.log(`📝 Appending ${key}:`, value);
          requestData.append(key, value);
        }
      });

      // Log FormData contents for debugging
      console.log('📋 FormData contents:');
      for (let [key, value] of requestData.entries()) {
        console.log(`  ${key}:`, value instanceof File ? `File(${value.name}, ${value.size} bytes)` : value);
      }

      // Don't set Content-Type header - let browser set it with proper boundary
      const response = await api.post(`/land-owners/${id}`, requestData);
      console.log('✅ Land owner updated successfully with files:', response.data);
      return response.data;
    } else {
      console.log('📄 Using JSON for regular update');
      // Use regular JSON for non-file data
      requestData = { ...data };
      headers['Content-Type'] = 'application/json';

      // Remove file fields if they're existing URLs to preserve them
      ['photo', 'nid_front', 'nid_back', 'passport_photo'].forEach(field => {
        if (requestData[field] && typeof requestData[field] === 'string' && requestData[field].startsWith('/landowners/')) {
          console.log(`🖼️ Removing existing ${field} URL from request data to preserve existing file`);
          delete requestData[field];
        } else if (requestData[field] === '') {
          console.log(`🗑️ Removing empty ${field} field to preserve existing file`);
          delete requestData[field];
        }
      });

      console.log('📋 JSON data being sent:', requestData);

      const response = await api.put(`/land-owners/${id}`, requestData, { headers });
      console.log('✅ Land owner updated successfully:', response.data);
      return response.data;
    }
  } catch (error) {
    console.error(`❌ Error updating land owner ${id}:`, error);
    
    // Log detailed error information
    if (error.response) {
      console.error('📊 Response status:', error.response.status);
      console.error('📄 Response data:', error.response.data);
      console.error('📋 Response headers:', error.response.headers);

      // Return the error response data for better error handling
      if (error.response.data) {
        return error.response.data;
      }
    } else if (error.request) {
      console.error('📡 Request was made but no response received:', error.request);
    } else {
      console.error('⚙️ Error setting up request:', error.message);
    }
    
    // Re-throw for handling in component
    throw error;
  }
};

// Delete land owner
export const delete_ = async (id) => {
  try {
    const response = await api.delete(`/land-owners/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting land owner ${id}:`, error);
    throw error;
  }
};

// Get land owners for dropdown (if this endpoint exists)
export const getForDropdown = async () => {
  try {
    const response = await api.get('/land-owners/dropdown');
    return response.data;
  } catch (error) {
    console.error('Error fetching land owners for dropdown:', error);
    throw error;
  }
};

// Get statistics (if this endpoint exists)
export const getStatistics = async () => {
  try {
    const response = await api.get('/land-owners/statistics');
    return response.data;
  } catch (error) {
    console.error('Error fetching land owner statistics:', error);
    throw error;
  }
};

// Audit Log API functions

// Get all audit logs
export const getAllAudits = async (params = {}) => {
  try {
    const response = await api.get('/land-owners-audits', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching land owner audit logs:', error);
    throw error;
  }
};

// Get audit statistics
export const getAuditStats = async () => {
  try {
    const response = await api.get('/land-owners-audit-stats');
    return response.data;
  } catch (error) {
    console.error('Error fetching audit statistics:', error);
    throw error;
  }
};

// Get detailed audit history for a specific land owner
export const getDetailedAuditHistory = async (id, params = {}) => {
  try {
    const response = await api.get(`/land-owners/${id}/detailed-audit-history`, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching detailed audit history:', error);
    throw error;
  }
};

// Activate a land owner
export const activate = async (id) => {
  try {
    const response = await api.patch(`/land-owners/${id}/activate`);
    return response.data;
  } catch (error) {
    console.error('Error activating land owner:', error);
    throw error;
  }
};

// Deactivate a land owner
export const deactivate = async (id) => {
  try {
    const response = await api.patch(`/land-owners/${id}/deactivate`);
    return response.data;
  } catch (error) {
    console.error('Error deactivating land owner:', error);
    throw error;
  }
};


