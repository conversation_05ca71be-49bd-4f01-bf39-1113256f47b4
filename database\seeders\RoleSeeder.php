<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'Super Admin',
                'description' => 'Full system access with all permissions',
                'accessible_modules' => ['dashboard', 'analytics', 'land-owners', 'land-acquisition', 'lifecycle', 'country', 'language', 'currency', 'customers', 'orders', 'components', 'reports', 'role', 'word-assistant', 'settings'],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'analytics' => ['read'],
                    'land-owners' => ['create', 'read', 'update', 'delete'],
                    'land-acquisition' => ['create', 'read', 'update', 'delete'],
                    'lifecycle' => ['create', 'read', 'update', 'delete'],
                    'country' => ['create', 'read', 'update', 'delete'],
                    'language' => ['create', 'read', 'update', 'delete'],
                    'currency' => ['create', 'read', 'update', 'delete'],
                    'customers' => ['create', 'read', 'update', 'delete'],
                    'orders' => ['create', 'read', 'update', 'delete'],
                    'components' => ['read'],
                    'reports' => ['read', 'export'],
                    'role' => ['create', 'read', 'update', 'delete'],
                    'word-assistant' => ['read', 'use'],
                    'settings' => ['read', 'update']
                ],
                'status' => 'active',
                'users_count' => 2
            ],
            [
                'name' => 'Admin',
                'description' => 'Administrative access with limited system settings',
                'accessible_modules' => ['dashboard', 'analytics', 'land-owners', 'land-acquisition', 'lifecycle', 'country', 'language', 'currency', 'customers', 'orders', 'reports', 'role', 'settings'],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'analytics' => ['read'],
                    'land-owners' => ['create', 'read', 'update', 'delete'],
                    'land-acquisition' => ['create', 'read', 'update', 'delete'],
                    'lifecycle' => ['read', 'update'],
                    'country' => ['create', 'read', 'update', 'delete'],
                    'language' => ['create', 'read', 'update', 'delete'],
                    'currency' => ['create', 'read', 'update', 'delete'],
                    'customers' => ['create', 'read', 'update'],
                    'orders' => ['read', 'update'],
                    'reports' => ['read', 'export'],
                    'role' => ['read'],
                    'settings' => ['read']
                ],
                'status' => 'active',
                'users_count' => 5
            ],
            [
                'name' => 'Manager',
                'description' => 'Management level access to land and owner records',
                'accessible_modules' => ['dashboard', 'analytics', 'land-owners', 'land-acquisition', 'lifecycle', 'country', 'language', 'currency', 'customers', 'reports'],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'analytics' => ['read'],
                    'land-owners' => ['create', 'read', 'update'],
                    'land-acquisition' => ['create', 'read', 'update'],
                    'lifecycle' => ['read'],
                    'country' => ['create', 'read', 'update'],
                    'language' => ['create', 'read', 'update'],
                    'currency' => ['create', 'read', 'update'],
                    'customers' => ['read', 'update'],
                    'reports' => ['read']
                ],
                'status' => 'active',
                'users_count' => 8
            ],
            [
                'name' => 'Editor',
                'description' => 'Can create and edit records but not delete',
                'accessible_modules' => ['dashboard', 'land-owners', 'land-acquisition', 'customers', 'orders'],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'land-owners' => ['create', 'read', 'update'],
                    'land-acquisition' => ['create', 'read', 'update'],
                    'customers' => ['create', 'read', 'update'],
                    'orders' => ['create', 'read', 'update']
                ],
                'status' => 'active',
                'users_count' => 12
            ],
            [
                'name' => 'Viewer',
                'description' => 'Read-only access to view records and reports',
                'accessible_modules' => ['dashboard', 'analytics', 'land-owners', 'land-acquisition', 'customers', 'reports'],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'analytics' => ['read'],
                    'land-owners' => ['read'],
                    'land-acquisition' => ['read'],
                    'customers' => ['read'],
                    'reports' => ['read']
                ],
                'status' => 'active',
                'users_count' => 25
            ],
            [
                'name' => 'Guest',
                'description' => 'Limited read access to basic information',
                'accessible_modules' => ['dashboard'],
                'module_permissions' => [
                    'dashboard' => ['read']
                ],
                'status' => 'inactive',
                'users_count' => 3
            ]
        ];

        foreach ($roles as $roleData) {
            Role::updateOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );
        }
    }
}
