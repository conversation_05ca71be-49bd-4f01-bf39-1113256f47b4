// Centralized API configuration with automatic environment detection
export const getApiBaseUrl = () => {
  const currentHost = window.location.hostname;
  const currentPort = window.location.port;
  const currentProtocol = window.location.protocol;
  
  console.log('🌐 Environment Detection:', {
    hostname: currentHost,
    port: currentPort,
    protocol: currentProtocol,
    pathname: window.location.pathname,
    href: window.location.href
  });

  // Local Development Detection
  const isLocalDevelopment = (
    currentHost === 'localhost' || 
    currentHost === '127.0.0.1' || 
    currentHost.startsWith('192.168.') ||
    currentHost.endsWith('.local') ||
    import.meta.env.DEV
  );

  if (isLocalDevelopment) {
    console.log('🏠 Detected Local Development Environment');
    
    // XAMPP Setup Detection
    if (window.location.pathname.includes('real-estate-management')) {
      const xamppUrl = `${currentProtocol}//${currentHost}${currentPort ? ':' + currentPort : ''}/real-estate-management/api`;
      console.log('📁 Using XAMPP URL:', xamppUrl);
      return xamppUrl;
    }
    
    // Laravel Development Server Detection (php artisan serve)
    if (currentPort === '8000' || currentPort === '3000') {
      const devServerUrl = `${currentProtocol}//${currentHost}:${currentPort}/api`;
      console.log('🚀 Using Laravel Dev Server URL:', devServerUrl);
      return devServerUrl;
    }
    
    // Default local development
    const defaultLocalUrl = 'http://127.0.0.1:8000/api';
    console.log('⚙️ Using Default Local URL:', defaultLocalUrl);
    return defaultLocalUrl;
  }

  // Production Environment
  console.log('🌐 Detected Production Environment');
  const productionUrl = `${currentProtocol}//${currentHost}/api`;
  console.log('🏭 Using Production URL:', productionUrl);
  return productionUrl;
};

// Get the API base URL
export const API_BASE_URL = getApiBaseUrl();

// Log the selected API URL for debugging
console.log('🎯 Selected API Base URL:', API_BASE_URL);

// Function to test API connectivity
export const testApiConnectivity = async (url) => {
  try {
    const response = await fetch(`${url}/info`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API Connectivity Test Successful:', {
        url,
        environment: data.environment,
        app_url: data.app_url
      });
      return { success: true, data };
    }
    
    throw new Error(`HTTP ${response.status}`);
  } catch (error) {
    console.warn('❌ API Connectivity Test Failed:', { url, error: error.message });
    return { success: false, error: error.message };
  }
};

// Auto-detect and validate the best API URL
export const getValidatedApiUrl = async () => {
  const primaryUrl = getApiBaseUrl();
  
  // Test primary URL first
  const primaryTest = await testApiConnectivity(primaryUrl);
  if (primaryTest.success) {
    return primaryUrl;
  }
  
  console.warn('🔄 Primary API URL failed, trying fallback options...');
  
  // Fallback URLs to try
  const fallbackUrls = [
    'http://127.0.0.1:8000/api',
    'http://localhost:8000/api',
    `${window.location.protocol}//${window.location.host}/api`,
  ];
  
  // Remove duplicates and primary URL from fallbacks
  const uniqueFallbacks = [...new Set(fallbackUrls)].filter(url => url !== primaryUrl);
  
  for (const fallbackUrl of uniqueFallbacks) {
    const test = await testApiConnectivity(fallbackUrl);
    if (test.success) {
      console.log('✅ Found working fallback URL:', fallbackUrl);
      return fallbackUrl;
    }
  }
  
  console.error('❌ No working API URL found, using primary URL as last resort');
  return primaryUrl;
};

// Environment detection utilities
export const getEnvironmentInfo = () => {
  return {
    hostname: window.location.hostname,
    port: window.location.port,
    protocol: window.location.protocol,
    pathname: window.location.pathname,
    href: window.location.href,
    isLocal: window.location.hostname === 'localhost' || 
             window.location.hostname === '127.0.0.1' ||
             window.location.hostname.startsWith('192.168.') ||
             import.meta.env.DEV,
    apiUrl: API_BASE_URL
  };
};
