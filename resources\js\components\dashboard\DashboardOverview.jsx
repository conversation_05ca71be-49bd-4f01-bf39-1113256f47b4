import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import VisitorsChart from '@/components/ui/visitors-chart';
import ProjectTable from '@/components/ui/project-table';
import { useTranslation } from '@/hooks/useTranslation';
import {
  Users,
  ShoppingCart,
  DollarSign,
  Activity,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

const DashboardOverview = () => {
  const { t } = useTranslation();

  const stats = [
    {
      title: t('dashboard.statistics.totalRevenue'),
      value: "$45,231.00",
      change: "+20.1%",
      trend: "up",
      icon: DollarSign,
      description: t('dashboard.statistics.fromLastMonth')
    },
    {
      title: t('dashboard.statistics.subscriptions'),
      value: "+2350",
      change: "+180.1%",
      trend: "up",
      icon: Users,
      description: t('dashboard.statistics.fromLastMonth')
    },
    {
      title: t('dashboard.statistics.sales'),
      value: "+12,234",
      change: "+19%",
      trend: "up",
      icon: ShoppingCart,
      description: t('dashboard.statistics.fromLastMonth')
    },
    {
      title: t('dashboard.statistics.activeNow'),
      value: "+573",
      change: "+201",
      trend: "up",
      icon: Activity,
      description: t('dashboard.statistics.fromLastHour')
    }
  ];

  
  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('dashboard.title')}</h1>
          <p className="text-muted-foreground">
            {t('dashboard.description')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">{t('common.buttons.download')}</Button>
          <Button>{t('dashboard.viewReport')}</Button>
        </div>
      </div>

      {/* Stats grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => {
          const isPositive = stat.trend === 'up';
          const TrendIcon = isPositive ? ArrowUpRight : ArrowDownRight;

          return (
            <Card key={index} className="p-6">
              <CardContent className="p-0">
                {/* Header with title and trend indicator */}
                <div className="flex items-center justify-between mb-2">
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <div className="flex items-center text-xs">
                    <TrendIcon className={`mr-1 h-3 w-3 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
                    <span className={`font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                      {stat.change}
                    </span>
                  </div>
                </div>

                {/* Main value */}
                <div className="text-3xl font-bold text-foreground mb-2">
                  {stat.value}
                </div>

                {/* Description with trend icon */}
                <div className="flex items-center text-sm text-muted-foreground">
                  <TrendIcon className={`mr-1 h-3 w-3 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
                  <span>{stat.description}</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Charts and recent activity */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Total Visitors Chart */}
        <VisitorsChart />

     

       
      </div>

      {/* Project Table Section */}
      <div className="mt-6">
        <ProjectTable />
      </div>
    </div>
  );
};

export default DashboardOverview;
