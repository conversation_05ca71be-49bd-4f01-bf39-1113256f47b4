import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import * as landAcquisitionAPI from '../../services/landAcquisitionAPI';
import { landDocumentAPI } from '../../services/landDocumentAPI';
import * as countryAPI from '../../services/countryAPI';
import * as stateAPI from '../../services/stateAPI';
import * as cityAPI from '../../services/cityAPI';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useTranslation } from '@/hooks/useTranslation';
import SimpleImageUpload from '@/components/ui/SimpleImageUpload';
import ProfileImageUpload from '@/components/ui/ProfileImageUpload';
import DocumentUpload from '@/components/ui/DocumentUpload';
import LandDocumentRepeater from '@/components/ui/LandDocumentRepeater';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  MapPin,
  FileText,
  DollarSign,
  BarChart3,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';

// SweetAlert2 configuration
const showAlert = {
  success: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'success',
      confirmButtonText: 'OK',
      confirmButtonColor: '#10b981',
      customClass: {
        popup: 'rounded-lg swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      }
    });
  },
  error: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ef4444',
      customClass: {
        popup: 'rounded-lg swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      }
    });
  },
  warning: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#f59e0b',
      customClass: {
        popup: 'rounded-lg swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      }
    });
  },
  confirm: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: 'Yes, Create',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#10b981',
      cancelButtonColor: '#6b7280',
      customClass: {
        popup: 'rounded-lg swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      }
    });
  },
  loading: (title, text) => {
    return Swal.fire({
      title,
      text,
      icon: 'info',
      allowOutsideClick: false,
      allowEscapeKey: false,
      showConfirmButton: false,
      customClass: {
        popup: 'rounded-lg swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high',
        title: 'text-lg font-semibold',
        content: 'text-sm text-gray-600'
      },
      didOpen: () => {
        Swal.showLoading();
      }
    });
  }
};

const LandAcquisitionPage = () => {
  const [landAcquisitions, setLandAcquisitions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statistics, setStatistics] = useState({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [landOwners, setLandOwners] = useState([]);
  const [isNewLandOwner, setIsNewLandOwner] = useState(false);
  
  // Geographic data state
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [loadingStates, setLoadingStates] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);

  const [formData, setFormData] = useState({
    record_dag: '',
    khatian: '',
    mauza: '',
    land_size: '',
    acquisition_price: '',
    // For existing land owner selection
    landOwners_id: '',
    // Land owner fields (for new land owner)
    first_name: '',
    last_name: '',
    father_name: '',
    mother_name: '',
    phone: '',
    nid_number: '',
    email: '',
    address: '',
    photo: null, // Use null instead of empty string
    // Document upload fields
    document_type: 'nid',
    nid_front: null, // Use null instead of empty string
    nid_back: null, // Use null instead of empty string
    passport_photo: null, // Use null instead of empty string
    // Land documents
    documents: [],
    // Land address fields - simplified structure with dropdowns
    land_address: {
      country_id: '',
      state_id: '',
      city_id: '',
      specific_address: ''
    }
  });
  const [editFormData, setEditFormData] = useState({
    record_dag: '',
    khatian: '',
    mauza: '',
    land_size: '',
    acquisition_price: '',
    // Land owner fields for editing
    first_name: '',
    last_name: '',
    father_name: '',
    mother_name: '',
    phone: '',
    email: '',
    address: '',
    photo: null, // Use null instead of empty string
    // Land documents
    documents: [],
    existing_documents: [],
    // Land address fields - simplified structure with dropdowns
    land_address: {
      country_id: '',
      state_id: '',
      city_id: '',
      specific_address: ''
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // API functions
  const fetchLandAcquisitions = async (search = '', page = 1, itemsPerPage = 10) => {
    try {
      setLoading(true);

      const params = { page, per_page: itemsPerPage };
      if (search.trim()) {
        params.search = search.trim();
      }

      const result = await landAcquisitionAPI.getAll(params);
      console.log('🔍 API Response:', result);

      if (result.success) {
        // Handle different response formats robustly
        const acquisitionsData = result.data?.data || result.data || [];
        console.log('📊 Acquisitions Data:', acquisitionsData, 'Type:', typeof acquisitionsData, 'Is Array:', Array.isArray(acquisitionsData));
        setLandAcquisitions(Array.isArray(acquisitionsData) ? acquisitionsData : []);
        setStatistics(result.statistics || {});

        // Set pagination data (only if paginated response)
        if (result.data?.current_page) {
          setCurrentPage(result.data.current_page || 1);
          setTotalPages(result.data.last_page || 1);
          setTotalRecords(result.data.total || 0);
        } else {
          // Non-paginated response
          setCurrentPage(1);
          setTotalPages(1);
          setTotalRecords(Array.isArray(acquisitionsData) ? acquisitionsData.length : 0);
        }
      } else {
        console.error('API returned error:', result);
      }
    } catch (error) {
      console.error('Error fetching land acquisitions:', error);
      showAlert.error('Error!', 'Failed to fetch land acquisitions. Please try again.');
      // Fallback to empty data on error
      setLandAcquisitions([]);
      setStatistics({});
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const result = await landAcquisitionAPI.getStatistics();

      if (result.success) {
        setStatistics(result.data || {});
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  // Geographic data functions
  const fetchCountries = async () => {
    try {
      setLoadingCountries(true);
      const result = await countryAPI.getAll();
      if (result.success) {
        setCountries(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
      setCountries([]);
    } finally {
      setLoadingCountries(false);
    }
  };

  const fetchStates = async (countryId) => {
    if (!countryId) {
      setStates([]);
      setCities([]);
      return;
    }
    
    try {
      setLoadingStates(true);
      const result = await stateAPI.getByCountry(countryId);
      if (result.success) {
        setStates(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching states:', error);
      setStates([]);
    } finally {
      setLoadingStates(false);
    }
  };

  const fetchCities = async (stateId) => {
    if (!stateId) {
      setCities([]);
      return;
    }
    
    try {
      setLoadingCities(true);
      const result = await cityAPI.getByState(stateId);
      if (result.success) {
        setCities(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      setCities([]);
    } finally {
      setLoadingCities(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    if (!formData.record_dag || !formData.khatian || !formData.mauza ||
        !formData.land_size || !formData.acquisition_price) {
      showAlert.warning('Validation Error!', 'Please fill in all required land acquisition fields.');
      return;
    }

    // Validate land owner data based on checkbox
    if (isNewLandOwner) {
      // Validate new land owner fields
      if (!formData.first_name || !formData.last_name || !formData.father_name || !formData.address) {
        showAlert.warning('Validation Error!', 'Please fill in all required land owner fields: First Name, Last Name, Father\'s Name, and Address.');
        return;
      }
      // Clear landOwners_id when creating new land owner
      formData.landOwners_id = '';
    } else {
      // Validate existing land owner selection
      if (!formData.landOwners_id) {
        showAlert.warning('Validation Error!', 'Please select a land owner.');
        return;
      }
      // Ensure landOwners_id is not empty string but a valid number
      if (formData.landOwners_id === '' || formData.landOwners_id === null) {
        showAlert.warning('Validation Error!', 'Please select a valid land owner.');
        return;
      }
    }

    setIsSubmitting(true);

    // Show loading alert
    showAlert.loading(
      'Creating Record...',
      'Please wait while we create the land acquisition record.'
    );

    try {
      // Debug logging for new land owner creation
      if (isNewLandOwner) {
        console.log('🔍 Creating new land owner with data:', {
          first_name: formData.first_name,
          last_name: formData.last_name,
          father_name: formData.father_name,
          nid_number: formData.nid_number,
          document_type: formData.document_type,
          nid_front: formData.nid_front ? 'File uploaded' : 'No file',
          nid_back: formData.nid_back ? 'File uploaded' : 'No file', 
          passport_photo: formData.passport_photo ? 'File uploaded' : 'No file',
          photo: formData.photo ? 'File uploaded' : 'No file'
        });
        
        // Debug photo field specifically
        console.log('📷 Photo field debug:', {
          type: typeof formData.photo,
          isFile: formData.photo instanceof File,
          value: formData.photo instanceof File ? `File(${formData.photo.name}, ${formData.photo.size} bytes)` : formData.photo,
          isEmpty: !formData.photo || formData.photo === '' || formData.photo === null
        });
      }

      const result = await landAcquisitionAPI.create(formData);

      if (result.success) {
        // Upload documents if any
        if (formData.documents && formData.documents.length > 0) {
          try {
            const documentsToUpload = formData.documents.filter(doc => doc.document_file);
            if (documentsToUpload.length > 0) {
              await landDocumentAPI.bulkUpload(result.data.id, documentsToUpload);
            }
          } catch (docError) {
            console.error('Error uploading documents:', docError);
            // Show warning but don't fail the main creation
            showAlert.warning(
              'Partial Success',
              'Land acquisition created but some documents failed to upload. You can add them later.'
            );
          }
        }

        // Reset form
        setFormData({
          record_dag: '',
          khatian: '',
          mauza: '',
          land_size: '',
          acquisition_price: '',
          landOwners_id: '',
          first_name: '',
          last_name: '',
          father_name: '',
          mother_name: '',
          phone: '',
          nid_number: '',
          email: '',
          address: '',
          photo: null,
          // Document upload fields
          document_type: 'nid',
          nid_front: null,
          nid_back: null,
          passport_photo: null,
          documents: [],
          // Land address fields
          land_address: {
            plot_no: '',
            road: '',
            area: '',
            upazila: '',
            thana: '',
            city: '',
            district: '',
            country: 'Bangladesh',
            zip_code: ''
          }
        });

        // Reset checkbox
        setIsNewLandOwner(false);

        // Close modal
        setShowAddForm(false);

        // Refresh data
        fetchLandAcquisitions(searchTerm, currentPage, perPage);
        fetchStatistics();

        // Success alert
        showAlert.success(
          'Success!',
          'Land acquisition record created successfully!'
        );
      } else {
        // Error alert
        showAlert.error(
          'Error!',
          result.message || 'Unknown error occurred'
        );
      }
    } catch (error) {
      console.error('❌ Error creating land acquisition:', error);
      console.error('❌ Error response:', error.response?.data);
      
      // Network/Server error alert
      showAlert.error(
        'Error!',
        error.response?.data?.message || 'Error creating record. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const fetchLandOwners = async () => {
    try {
      console.log('🔄 Fetching land owners...');
      const result = await landAcquisitionAPI.getLandOwnersDropdown();
      console.log('👥 Land Owners API Response:', result);

      if (result && result.success && Array.isArray(result.data)) {
        const ownersData = result.data;
        console.log('👥 Owners Data:', ownersData, 'Type:', typeof ownersData, 'Is Array:', Array.isArray(ownersData));
        setLandOwners(ownersData);
      } else {
        console.warn('Invalid land owners response format:', result);
        setLandOwners([]);
      }
    } catch (error) {
      console.error('Error fetching land owners:', error);
      setLandOwners([]);
    }
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      // Handle nested fields like 'land_address.plot_no'
      const [parentField, childField] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parentField]: {
          ...prev[parentField],
          [childField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleEditInputChange = (field, value) => {
    if (field.includes('.')) {
      // Handle nested fields like 'land_address.plot_no'
      const [parentField, childField] = field.split('.');
      setEditFormData(prev => ({
        ...prev,
        [parentField]: {
          ...prev[parentField],
          [childField]: value
        }
      }));
    } else {
      setEditFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleEditRecord = async (record) => {
    setEditingRecord(record);
    setEditFormData({
      record_dag: record.record_dag,
      khatian: record.khatian,
      mauza: record.mauza,
      land_size: record.land_size,
      acquisition_price: record.acquisition_price,
      // Populate land owner fields from the relationship
      first_name: record.land_owner?.first_name || '',
      last_name: record.land_owner?.last_name || '',
      father_name: record.land_owner?.father_name || '',
      mother_name: record.land_owner?.mother_name || '',
      phone: record.land_owner?.phone || '',
      email: record.land_owner?.email || '',
      address: record.land_owner?.address || '',
      photo: record.land_owner?.photo || '',
      documents: [],
      existing_documents: [],
      // Populate land address fields from the relationship
      land_address: {
        country_id: record.land_address?.country_id || '',
        state_id: record.land_address?.state_id || '',
        city_id: record.land_address?.city_id || '',
        specific_address: record.land_address?.specific_address || ''
      }
    });

    // Load existing documents (non-blocking)
    setShowEditForm(true); // Show form immediately
    
    // Load states and cities if country and state are selected
    if (record.land_address?.country_id) {
      fetchStates(record.land_address.country_id);
      if (record.land_address?.state_id) {
        fetchCities(record.land_address.state_id);
      }
    }
    
    // Load documents asynchronously without blocking the form
    try {
      const documentsResult = await landDocumentAPI.getByLandAcquisition(record.id);
      if (documentsResult.success) {
        setEditFormData(prev => ({
          ...prev,
          existing_documents: Array.isArray(documentsResult.data) ? documentsResult.data : []
        }));
      }
    } catch (error) {
      console.error('Error loading documents (non-blocking):', error);
      // Check if it's an auth error
      if (error.response?.status === 401) {
        showAlert.error('Authentication Error!', 'Please login again to continue.');
        // Don't redirect here, let the interceptor handle it
        return;
      }
      // Don't block the edit form if documents can't be loaded
    }
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    if (!editFormData.record_dag || !editFormData.khatian || !editFormData.mauza ||
        !editFormData.land_size || !editFormData.acquisition_price ||
        !editFormData.first_name || !editFormData.last_name || !editFormData.father_name || !editFormData.address) {
      showAlert.warning('Validation Error!', 'Please fill in all required fields: First Name, Last Name, Father\'s Name, and Address.');
      return;
    }

    setIsUpdating(true);

    // Show loading alert
    showAlert.loading(
      'Updating Record...',
      'Please wait while we update the land acquisition record.'
    );

    try {
      // Debug logging
      console.log('📝 Edit form data before submission:', editFormData);
      console.log('📷 Photo data type:', typeof editFormData.photo);
      console.log('📷 Photo instanceof File:', editFormData.photo instanceof File);
      
      const result = await landAcquisitionAPI.update(editingRecord.id, editFormData);
      console.log('✅ Update API Response:', result);

      if (result.success) {
        // Handle document updates if any
        if (editFormData.documents && editFormData.documents.length > 0) {
          try {
            const documentsToUpload = editFormData.documents.filter(doc => doc.document_file);
            if (documentsToUpload.length > 0) {
              await landDocumentAPI.bulkUpload(editingRecord.id, documentsToUpload);
            }
          } catch (docError) {
            console.error('Error uploading documents:', docError);
            // Show warning but don't fail the main update
            showAlert.warning(
              'Partial Success',
              'Land acquisition updated but some documents failed to upload. You can add them later.'
            );
          }
        }

        // Reset form
        setEditFormData({
          record_dag: '',
          khatian: '',
          mauza: '',
          land_size: '',
          acquisition_price: '',
          first_name: '',
          last_name: '',
          father_name: '',
          mother_name: '',
          phone: '',
          email: '',
          address: '',
          photo: null,
          documents: [],
          existing_documents: [],
          // Land address fields
          land_address: {
            plot_no: '',
            road: '',
            area: '',
            upazila: '',
            thana: '',
            city: '',
            district: '',
            country: 'Bangladesh',
            zip_code: ''
          }
        });

        // Close modal
        setShowEditForm(false);
        setEditingRecord(null);

        // Refresh data
        fetchLandAcquisitions(searchTerm, currentPage, perPage);
        fetchStatistics();

        // Success alert
        showAlert.success(
          'Success!',
          'Land acquisition record updated successfully!'
        );
      } else {
        // Error alert
        showAlert.error(
          'Error!',
          result.message || 'Unknown error occurred'
        );
      }
    } catch (error) {
      console.error('❌ Error updating land acquisition:', error);
      console.error('❌ Error response:', error.response?.data);
      
      // Network/Server error alert
      showAlert.error(
        'Error!',
        error.response?.data?.message || 'Error updating record. Please try again.'
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteRecord = async (record) => {
    // Show confirmation dialog
    const result = await showAlert.confirm(
      'Confirm Deletion',
      `Are you sure you want to delete the record "${record.record_dag}"? This action cannot be undone.`
    );

    if (!result.isConfirmed) {
      return; // User cancelled
    }

    // Show loading alert
    showAlert.loading(
      'Deleting Record...',
      'Please wait while we delete the land acquisition record.'
    );

    try {
      const result = await landAcquisitionAPI.delete_(record.id);

      if (result.success) {
        // Refresh data
        fetchLandAcquisitions(searchTerm, currentPage, perPage);
        fetchStatistics();

        // Success alert
        showAlert.success(
          'Deleted!',
          'Land acquisition record has been deleted successfully.'
        );
      } else {
        // Error alert
        showAlert.error(
          'Error!',
          result.message || 'Failed to delete the record'
        );
      }
    } catch (error) {
      console.error('Error deleting land acquisition:', error);
      // Network/Server error alert
      showAlert.error(
        'Error!',
        'Error deleting record. Please try again.'
      );
    }
  };

  // Geographic field change handlers
  const handleCountryChange = (countryId, isEditForm = false) => {
    if (isEditForm) {
      handleEditInputChange('land_address.country_id', countryId);
      handleEditInputChange('land_address.state_id', '');
      handleEditInputChange('land_address.city_id', '');
    } else {
      handleInputChange('land_address.country_id', countryId);
      handleInputChange('land_address.state_id', '');
      handleInputChange('land_address.city_id', '');
    }
    
    // Fetch states for the selected country
    if (countryId) {
      fetchStates(countryId);
    } else {
      setStates([]);
      setCities([]);
    }
  };

  const handleStateChange = (stateId, isEditForm = false) => {
    if (isEditForm) {
      handleEditInputChange('land_address.state_id', stateId);
      handleEditInputChange('land_address.city_id', '');
    } else {
      handleInputChange('land_address.state_id', stateId);
      handleInputChange('land_address.city_id', '');
    }
    
    // Fetch cities for the selected state
    if (stateId) {
      fetchCities(stateId);
    } else {
      setCities([]);
    }
  };

  const handleCityChange = (cityId, isEditForm = false) => {
    if (isEditForm) {
      handleEditInputChange('land_address.city_id', cityId);
    } else {
      handleInputChange('land_address.city_id', cityId);
    }
  };

  useEffect(() => {
    fetchLandAcquisitions('', 1, perPage);
    fetchStatistics();
    fetchLandOwners();
  }, [perPage]);

  // Fetch countries on component mount
  useEffect(() => {
    fetchCountries();
  }, []);

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCurrentPage(1); // Reset to first page when searching
      fetchLandAcquisitions(searchTerm, 1, perPage);
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, perPage]);

  // Handle page changes
  useEffect(() => {
    if (currentPage > 1) {
      fetchLandAcquisitions(searchTerm, currentPage, perPage);
    }
  }, [currentPage]);

  // Effect to handle clearing form data when switching between modes
  useEffect(() => {
    if (isNewLandOwner) {
      // Clear existing land owner selection when creating new
      setFormData(prev => ({
        ...prev,
        landOwners_id: ''
      }));
    } else {
      // Clear new land owner fields when selecting existing
      setFormData(prev => ({
        ...prev,
        first_name: '',
        last_name: '',
        father_name: '',
        mother_name: '',
        phone: '',
        nid_number: '',
        email: '',
        address: '',
        photo: null
      }));
    }
  }, [isNewLandOwner]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('En-BD', {
      style: 'currency',
      currency: 'BDT',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatLandSize = (size) => {
    return `${size} decimal`;
  };

  // Since we're doing server-side filtering, we don't need client-side filtering
  const filteredData = Array.isArray(landAcquisitions) ? landAcquisitions : [];
  console.log('🎯 Filtered Data:', filteredData, 'Length:', filteredData.length, 'Is Array:', Array.isArray(filteredData));

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading land acquisitions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Land Acquisition</h1>
          <p className="text-muted-foreground">
            Manage land acquisition records and ownership details
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add New Record
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-6">
          <CardContent className="p-0">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-muted-foreground">Total Records</p>
              <div className="flex items-center text-xs">
                <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
                <span className="font-medium text-green-600">+12.5%</span>
              </div>
            </div>
            <div className="text-3xl font-bold text-foreground mb-2">
              {statistics.total_records}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
              <span>Land acquisition records</span>
            </div>
          </CardContent>
        </Card>

        <Card className="p-6">
          <CardContent className="p-0">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-muted-foreground">Total Land Size</p>
              <div className="flex items-center text-xs">
                <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
                <span className="font-medium text-green-600">+8.2%</span>
              </div>
            </div>
            <div className="text-3xl font-bold text-foreground mb-2">
              {statistics.total_land_size}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
              <span>Decimal of land acquired</span>
            </div>
          </CardContent>
        </Card>

        <Card className="p-6">
          <CardContent className="p-0">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-muted-foreground">Total Value</p>
              <div className="flex items-center text-xs">
                <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
                <span className="font-medium text-green-600">+15.3%</span>
              </div>
            </div>
            <div className="text-3xl font-bold text-foreground mb-2">
              {formatCurrency(statistics.total_acquisition_value)}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
              <span>Total acquisition cost</span>
            </div>
          </CardContent>
        </Card>

        <Card className="p-6">
          <CardContent className="p-0">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-muted-foreground">Avg. Price/Decimal</p>
              <div className="flex items-center text-xs">
                <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
                <span className="font-medium text-green-600">+4.1%</span>
              </div>
            </div>
            <div className="text-3xl font-bold text-foreground mb-2">
              {formatCurrency(statistics.average_price_per_decimal)}
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <BarChart3 className="mr-1 h-3 w-3 text-green-600" />
              <span>Average price per decimal</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Land Acquisition Records</CardTitle>
          <CardDescription>
            View and manage all land acquisition records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1 no-focus-outline">
              <style jsx>{`
                .no-focus-outline input {
                  transition: all 0.2s ease-in-out;
                  transform: scale(1);
                }
                .no-focus-outline input:focus {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:focus-visible {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:hover {
                  border-color: #9ca3af !important;
                  transform: scale(1.01) !important;
                }
              `}</style>
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by Record DAG, Khatian, Mauza, or Owner name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
          </div>

          {/* Data Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">S.N.</TableHead>
                  <TableHead>Record DAG</TableHead>
                  <TableHead>Khatian</TableHead>
                  <TableHead>Mauza</TableHead>
                  <TableHead>Land Size</TableHead>
                  <TableHead>Acquisition Price</TableHead>
                  <TableHead>Land Owner</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((record, index) => (
                  <TableRow key={record.id}>
                    <TableCell className="text-center text-muted-foreground">
                      {((currentPage - 1) * perPage) + index + 1}
                    </TableCell>
                    <TableCell className="font-medium">
                      <Badge variant="outline">{record.record_dag}</Badge>
                    </TableCell>
                    <TableCell>{record.khatian}</TableCell>
                    <TableCell>{record.mauza}</TableCell>
                    <TableCell>{formatLandSize(record.land_size)}</TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(record.acquisition_price)}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {record.land_owner?.name || 'N/A'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          S/O {record.land_owner?.father_name || 'N/A'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {record.land_owner?.phone || 'N/A'}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{new Date(record.created_at).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    
                          <DropdownMenuItem onClick={() => handleEditRecord(record)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Record
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => handleDeleteRecord(record)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Record
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination Controls */}
          {filteredData.length > 0 && (
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">
                  Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalRecords)} of {totalRecords} entries
                </span>
              </div>

              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">Rows per page</span>
                  <select
                    value={perPage}
                    onChange={(e) => {
                      setPerPage(Number(e.target.value));
                      setCurrentPage(1);
                    }}
                    className="border rounded px-2 py-1 text-sm bg-background"
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                </div>

                <div className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </div>

                <div className="flex items-center space-x-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(1)}
                    disabled={currentPage === 1}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronsLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={currentPage === totalPages}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronsRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {filteredData.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No records found matching your search.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add New Record Modal */}
      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Add New Land Acquisition Record</DialogTitle>
            <DialogDescription>
              Enter the land acquisition details and land owner information.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto pr-2">
            <form onSubmit={handleSubmit} className="space-y-6 ml-2">
              <style jsx>{`
                .no-focus-outline input {
                  transition: all 0.2s ease-in-out;
                  transform: scale(1);
                }
                .no-focus-outline input:focus {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:focus-visible {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:hover {
                  border-color: #9ca3af !important;
                  transform: scale(1.01) !important;
                }
              `}</style>

            {/* Land Owner Information - MOVED TO TOP */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-blue-600 border-b pb-2">Land Owner Information</h3>

              {/* Checkbox for new land owner */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="new_land_owner"
                  checked={isNewLandOwner}
                  onCheckedChange={setIsNewLandOwner}
                />
                <Label htmlFor="new_land_owner">Create New Land Owner</Label>
              </div>

              {!isNewLandOwner ? (
                // Existing Land Owner Dropdown
                <div className="space-y-2">
                  <Label htmlFor="landOwners_id">Select Land Owner *</Label>
                  <Select
                    value={formData.landOwners_id}
                    onValueChange={(value) => handleInputChange('landOwners_id', value)}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select an existing land owner" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(landOwners) && landOwners.length > 0 ? (
                        landOwners.map((owner) => (
                          <SelectItem key={owner.id} value={owner.id.toString()}>
                            {owner.first_name} {owner.last_name} - {owner.father_name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="" disabled>
                          No land owners available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>  
              ) : (
                // New Land Owner Form
                <div className="space-y-6 p-6 rounded-lg border">
                  <h4 className="text-md font-medium text-blue-700">New Land Owner Details</h4>
                  
                  {/* Profile Photo Section */}
                  <div className="border-b pb-6">
                    <ProfileImageUpload
                      value={formData.photo}
                      onChange={(file) => handleInputChange('photo', file)}
                      name={`${formData.first_name} ${formData.last_name}`.trim() || 'Owner'}
                      disabled={isSubmitting}
                    />
                  </div>

                  {/* Basic Information */}
                  <div className="space-y-4">
                    <h5 className="text-md font-medium text-gray-900">Basic Information</h5>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2 no-focus-outline">
                        <Label htmlFor="first_name">First Name *</Label>
                        <Input
                          id="first_name"
                          value={formData.first_name}
                          onChange={(e) => handleInputChange('first_name', e.target.value)}
                          placeholder="First Name"
                          required
                        />
                      </div>

                      <div className="space-y-2 no-focus-outline">
                        <Label htmlFor="last_name">Last Name *</Label>
                        <Input
                          id="last_name"
                          value={formData.last_name}
                          onChange={(e) => handleInputChange('last_name', e.target.value)}
                          placeholder="Last Name"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2 no-focus-outline">
                      <Label htmlFor="father_name">Father's Name *</Label>
                      <Input
                        id="father_name"
                        value={formData.father_name}
                        onChange={(e) => handleInputChange('father_name', e.target.value)}
                        placeholder="Abdul Karim"
                        required
                      />
                    </div>

                    <div className="space-y-2 no-focus-outline">
                      <Label htmlFor="mother_name">Mother's Name</Label>
                      <Input
                        id="mother_name"
                        value={formData.mother_name}
                        onChange={(e) => handleInputChange('mother_name', e.target.value)}
                        placeholder="Fatima Begum"
                      />
                    </div>

                    <div className="space-y-2 no-focus-outline">
                      <Label htmlFor="address">Address *</Label>
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => handleInputChange('address', e.target.value)}
                        placeholder="House 123, Road 456, Dhanmondi, Dhaka"
                        required
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2 no-focus-outline">
                        <Label htmlFor="phone">Phone</Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          placeholder="+880171234567"
                        />
                      </div>

                      <div className="space-y-2 no-focus-outline">
                        <Label htmlFor="nid_number">NID Number</Label>
                        <Input
                          id="nid_number"
                          value={formData.nid_number}
                          onChange={(e) => handleInputChange('nid_number', e.target.value)}
                          placeholder="1234567890123"
                        />
                      </div>
                    </div>

                    <div className="space-y-2 no-focus-outline">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  {/* Document Upload Section */}
                  <div className="border-t pt-6">
                    <DocumentUpload
                      documentType={formData.document_type}
                      onDocumentTypeChange={(type) => handleInputChange('document_type', type)}
                      nidFront={formData.nid_front}
                      onNidFrontChange={(file) => handleInputChange('nid_front', file)}
                      nidBack={formData.nid_back}
                      onNidBackChange={(file) => handleInputChange('nid_back', file)}
                      passportPhoto={formData.passport_photo}
                      onPassportPhotoChange={(file) => handleInputChange('passport_photo', file)}
                      disabled={isSubmitting}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Land Acquisition Details - MOVED TO MIDDLE */}
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-medium text-green-600 border-b pb-2">Land Acquisition Details</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="record_dag">Record DAG *</Label>
                  <Input
                    id="record_dag"
                    value={formData.record_dag}
                    onChange={(e) => handleInputChange('record_dag', e.target.value)}
                    placeholder="DAG-001"
                    required
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="khatian">Khatian *</Label>
                  <Input
                    id="khatian"
                    value={formData.khatian}
                    onChange={(e) => handleInputChange('khatian', e.target.value)}
                    placeholder="KH-2024-001"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="mauza">Mauza *</Label>
                <Input
                  id="mauza"
                  value={formData.mauza}
                  onChange={(e) => handleInputChange('mauza', e.target.value)}
                  placeholder="Dhanmondi"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="land_size">Land Size (decimal) *</Label>
                  <Input
                    id="land_size"
                    type="number"
                    step="0.01"
                    value={formData.land_size}
                    onChange={(e) => handleInputChange('land_size', e.target.value)}
                    placeholder="5.25"
                    required
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="acquisition_price">Acquisition Price (৳) *</Label>
                  <Input
                    id="acquisition_price"
                    type="number"
                    value={formData.acquisition_price}
                    onChange={(e) => handleInputChange('acquisition_price', e.target.value)}
                    placeholder="2500000"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Land Address Section */}
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-medium text-blue-600 border-b pb-2">Land Address</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="country">Country</Label>
                  <Select
                    value={formData.land_address.country_id}
                    onValueChange={(value) => handleCountryChange(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingCountries ? (
                        <SelectItem value="" disabled>Loading countries...</SelectItem>
                      ) : (
                        countries.map((country) => (
                          <SelectItem key={country.id} value={country.id.toString()}>
                            {country.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="state">State/Province</Label>
                  <Select
                    value={formData.land_address.state_id}
                    onValueChange={(value) => handleStateChange(value)}
                    disabled={!formData.land_address.country_id}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select state/province" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingStates ? (
                        <SelectItem value="" disabled>Loading states...</SelectItem>
                      ) : states.length === 0 ? (
                        <SelectItem value="" disabled>No states available</SelectItem>
                      ) : (
                        states.map((state) => (
                          <SelectItem key={state.id} value={state.id.toString()}>
                            {state.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="city">City</Label>
                  <Select
                    value={formData.land_address.city_id}
                    onValueChange={(value) => handleCityChange(value)}
                    disabled={!formData.land_address.state_id}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select city" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingCities ? (
                        <SelectItem value="" disabled>Loading cities...</SelectItem>
                      ) : cities.length === 0 ? (
                        <SelectItem value="" disabled>No cities available</SelectItem>
                      ) : (
                        cities.map((city) => (
                          <SelectItem key={city.id} value={city.id.toString()}>
                            {city.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="specific_address">Specific Address</Label>
                  <Input
                    id="specific_address"
                    value={formData.land_address.specific_address}
                    onChange={(e) => handleInputChange('land_address.specific_address', e.target.value)}
                    placeholder="Plot 123, Road 4, Block A"
                  />
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAddForm(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Creating...' : 'Create Record'}
              </Button>
            </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Record Modal */}
      <Dialog open={showEditForm} onOpenChange={setShowEditForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>Edit Land Acquisition Record</DialogTitle>
            <DialogDescription>
              Update the details for the land acquisition record.
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto pr-2">
            <form id="editForm" onSubmit={handleUpdateSubmit} className="space-y-4 ml-2">
              <style jsx>{`
                .no-focus-outline input {
                  transition: all 0.2s ease-in-out;
                  transform: scale(1);
                }
                .no-focus-outline input:focus {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:focus-visible {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:hover {
                  border-color: #9ca3af !important;
                  transform: scale(1.01) !important;
                }
              `}</style>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="edit_record_dag">Record DAG</Label>
                <Input
                  id="edit_record_dag"
                  value={editFormData.record_dag}
                  onChange={(e) => handleEditInputChange('record_dag', e.target.value)}
                  placeholder="DAG-001"
                  required
                />
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="edit_khatian">Khatian</Label>
                <Input
                  id="edit_khatian"
                  value={editFormData.khatian}
                  onChange={(e) => handleEditInputChange('khatian', e.target.value)}
                  placeholder="KH-2024-001"
                  required
                />
              </div>
            </div>

            <div className="space-y-2 no-focus-outline">
              <Label htmlFor="edit_mauza">Mauza</Label>
              <Input
                id="edit_mauza"
                value={editFormData.mauza}
                onChange={(e) => handleEditInputChange('mauza', e.target.value)}
                placeholder="Dhanmondi"
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="edit_land_size">Land Size (decimal)</Label>
                <Input
                  id="edit_land_size"
                  type="number"
                  step="0.01"
                  value={editFormData.land_size}
                  onChange={(e) => handleEditInputChange('land_size', e.target.value)}
                  placeholder="5.25"
                  required
                />
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="edit_acquisition_price">Acquisition Price (৳)</Label>
                <Input
                  id="edit_acquisition_price"
                  type="number"
                  value={editFormData.acquisition_price}
                  onChange={(e) => handleEditInputChange('acquisition_price', e.target.value)}
                  placeholder="2500000"
                  required
                />
              </div>
            </div>

            {/* Land Owner Details */}
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-medium">Land Owner Information</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="edit_first_name">First Name *</Label>
                  <Input
                    id="edit_first_name"
                    value={editFormData.first_name}
                    onChange={(e) => handleEditInputChange('first_name', e.target.value)}
                    placeholder="First Name"
                    required
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="edit_last_name">Last Name *</Label>
                  <Input
                    id="edit_last_name"
                    value={editFormData.last_name}
                    onChange={(e) => handleEditInputChange('last_name', e.target.value)}
                    placeholder="Last Name"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="edit_father_name">Father's Name *</Label>
                <Input
                  id="edit_father_name"
                  value={editFormData.father_name}
                  onChange={(e) => handleEditInputChange('father_name', e.target.value)}
                  placeholder="Abdul Karim"
                  required
                />
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="edit_mother_name">Mother's Name</Label>
                <Input
                  id="edit_mother_name"
                  value={editFormData.mother_name}
                  onChange={(e) => handleEditInputChange('mother_name', e.target.value)}
                  placeholder="Fatima Begum"
                />
              </div>

              <div className="space-y-2 no-focus-outline">
                <Label htmlFor="edit_address">Address *</Label>
                <Input
                  id="edit_address"
                  value={editFormData.address}
                  onChange={(e) => handleEditInputChange('address', e.target.value)}
                  placeholder="House 123, Road 456, Dhanmondi, Dhaka"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="edit_phone">Phone</Label>
                  <Input
                    id="edit_phone"
                    value={editFormData.phone}
                    onChange={(e) => handleEditInputChange('phone', e.target.value)}
                    placeholder="+880171234567"
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="edit_email">Email</Label>
                  <Input
                    id="edit_email"
                    type="email"
                    value={editFormData.email}
                    onChange={(e) => handleEditInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <SimpleImageUpload
                  value={editFormData.photo}
                  onChange={(file) => handleEditInputChange('photo', file)}
                  label="Owner Photo (Optional)"
                  placeholder="Upload owner's photo (not required)"
                  disabled={isUpdating}
                />
                <p className="text-xs text-gray-500">Image upload is optional. You can update it later if needed.</p>
              </div>
            </div>

            {/* Land Address Section */}
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-medium text-blue-600 border-b pb-2">Land Address</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_country">Country</Label>
                  <Select
                    value={editFormData.land_address.country_id}
                    onValueChange={(value) => handleCountryChange(value, true)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingCountries ? (
                        <SelectItem value="" disabled>Loading countries...</SelectItem>
                      ) : (
                        countries.map((country) => (
                          <SelectItem key={country.id} value={country.id.toString()}>
                            {country.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit_state">State/Province</Label>
                  <Select
                    value={editFormData.land_address.state_id}
                    onValueChange={(value) => handleStateChange(value, true)}
                    disabled={!editFormData.land_address.country_id}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select state/province" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingStates ? (
                        <SelectItem value="" disabled>Loading states...</SelectItem>
                      ) : states.length === 0 ? (
                        <SelectItem value="" disabled>No states available</SelectItem>
                      ) : (
                        states.map((state) => (
                          <SelectItem key={state.id} value={state.id.toString()}>
                            {state.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit_city">City</Label>
                  <Select
                    value={editFormData.land_address.city_id}
                    onValueChange={(value) => handleCityChange(value, true)}
                    disabled={!editFormData.land_address.state_id}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select city" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingCities ? (
                        <SelectItem value="" disabled>Loading cities...</SelectItem>
                      ) : cities.length === 0 ? (
                        <SelectItem value="" disabled>No cities available</SelectItem>
                      ) : (
                        cities.map((city) => (
                          <SelectItem key={city.id} value={city.id.toString()}>
                            {city.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit_specific_address">Specific Address</Label>
                  <Input
                    id="edit_specific_address"
                    value={editFormData.land_address.specific_address}
                    onChange={(e) => handleEditInputChange('land_address.specific_address', e.target.value)}
                    placeholder="Plot 123, Road 4, Block A"
                  />
                </div>
              </div>
            </div>

            {/* Land Documents Section */}
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-medium text-purple-600 border-b pb-2">Land Documents</h3>
              <LandDocumentRepeater
                documents={editFormData.documents}
                existingDocuments={editFormData.existing_documents}
                onChange={(documents) => handleEditInputChange('documents', documents)}
                onDeleteExisting={async (documentId) => {
                  try {
                    await landDocumentAPI.delete(documentId);
                    // Remove from existing documents list
                    setEditFormData(prev => ({
                      ...prev,
                      existing_documents: Array.isArray(prev.existing_documents) 
                        ? prev.existing_documents.filter(doc => doc.id !== documentId)
                        : []
                    }));
                    showAlert.success('Success!', 'Document deleted successfully.');
                  } catch (error) {
                    console.error('Error deleting document:', error);
                    showAlert.error('Error!', 'Failed to delete document. Please try again.');
                  }
                }}
                disabled={isUpdating}
              />
            </div>
            </form>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setShowEditForm(false);
                setEditingRecord(null);
              }}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button type="submit" form="editForm" disabled={isUpdating}>
              {isUpdating ? 'Updating...' : 'Update Record'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LandAcquisitionPage;
