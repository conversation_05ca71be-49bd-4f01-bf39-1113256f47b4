import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './components/App';

console.log('React main.jsx loading...');

try {
  const appElement = document.getElementById('app');
  console.log('App element found:', appElement);
  
  if (!appElement) {
    throw new Error('Could not find app element');
  }
  
  const root = ReactDOM.createRoot(appElement);
  console.log('React root created');
  
  root.render(<App />);
  console.log('App rendered successfully');
} catch (error) {
  console.error('Error initializing React app:', error);
  // Show error message in the DOM
  document.body.innerHTML = `
    <div style="padding: 20px; color: red; font-family: Arial;">
      <h1>React App Error</h1>
      <p>Error: ${error.message}</p>
      <p>Check browser console for details.</p>
    </div>
  `;
}
  