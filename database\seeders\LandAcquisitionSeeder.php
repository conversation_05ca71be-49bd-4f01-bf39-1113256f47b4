<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LandAcquisition;
use App\Models\LandOwner;

class LandAcquisitionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $landAcquisitions = [
            [
                'record_dag' => 'DAG-001',
                'khatian' => 'KH-2024-001',
                'mauza' => 'Dhanmondi',
                'land_size' => 5.25,
                'acquisition_price' => 2500000,
                'landOwners_id' => 1,
            ],
            [
                'record_dag' => 'DAG-002',
                'khatian' => 'KH-2024-002',
                'mauza' => 'Gulshan',
                'land_size' => 3.75,
                'acquisition_price' => 4200000,
                'landOwners_id' => 2,
            ],
            [
                'record_dag' => 'DAG-003',
                'khatian' => 'KH-2024-003',
                'mauza' => 'Banani',
                'land_size' => 7.50,
                'acquisition_price' => 6750000,
                'landOwners_id' => 3,
            ],
            [
                'record_dag' => 'DAG-004',
                'khatian' => 'KH-2024-004',
                'mauza' => 'Uttara',
                'land_size' => 4.80,
                'acquisition_price' => 3840000,
                'landOwners_id' => 4,
            ],
            [
                'record_dag' => 'DAG-005',
                'khatian' => 'KH-2024-005',
                'mauza' => 'Mirpur',
                'land_size' => 6.20,
                'acquisition_price' => 3720000,
                'landOwners_id' => 5,
            ],
            [
                'record_dag' => 'DAG-006',
                'khatian' => 'KH-2024-006',
                'mauza' => 'Dhanmondi',
                'land_size' => 2.90,
                'acquisition_price' => 2030000,
                'landOwners_id' => 1,
            ],
            [
                'record_dag' => 'DAG-007',
                'khatian' => 'KH-2024-007',
                'mauza' => 'Gulshan',
                'land_size' => 8.15,
                'acquisition_price' => 8150000,
                'landOwners_id' => 2,
            ],
            [
                'record_dag' => 'DAG-008',
                'khatian' => 'KH-2024-008',
                'mauza' => 'Banani',
                'land_size' => 5.60,
                'acquisition_price' => 5600000,
                'landOwners_id' => 3,
            ],
        ];

        foreach ($landAcquisitions as $acquisition) {
            LandAcquisition::create($acquisition);
        }
    }
}
