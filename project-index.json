{
  "project": {
    "name": "Real Estate Management System",
    "type": "Laravel + React SPA",
    "description": "Full-stack real estate management application with Laravel backend API and React frontend",
    "version": "1.0.0",
    "tech_stack": {
      "backend": ["Laravel 12", "PHP 8.2+", "MySQL", "Laravel Sanctum"],
      "frontend": ["React 18", "Vite", "TailwindCSS", "React Router"],
      "tools": ["Composer", "NPM", "Artisan CLI", "PHPUnit"]
    }
  },
                "post-create-project-cmd": [
                    "@php artisan key:generate --ansi",
                    "@php -r \"file_exists('database\/database.sqlite') || touch('database\/database.sqlite');\"",
                    "@php artisan migrate --graceful --ansi"
                ],
                "dev": [
                    "Composer\\Config::disableProcessTimeout",
                    "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"
                ],
                "test": [
                    "@php artisan config:clear --ansi",
                    "@php artisan test"
                ]
            },
            "extra": {
                "laravel": {
                    "dont-discover": []
                }
            },
            "config": {
                "optimize-autoloader": true,
                "preferred-install": "dist",
                "sort-packages": true,
                "allow-plugins": {
                    "pestphp\/pest-plugin": true,
                    "php-http\/discovery": true
                }
            },
            "minimum-stability": "stable",
            "prefer-stable": true
        },
        "package": {
            "name": "laravel-react-dashboard",
            "version": "1.0.0",
            "private": true,
            "scripts": {
                "dev": "vite",
                "build": "vite build",
                "preview": "vite preview"
            },
            "dependencies": {
                "@radix-ui\/react-avatar": "^1.1.10",
                "@radix-ui\/react-checkbox": "^1.3.2",
                "@radix-ui\/react-dialog": "^1.1.14",
                "@radix-ui\/react-dropdown-menu": "^2.1.15",
                "@radix-ui\/react-label": "^2.1.7",
                "@radix-ui\/react-navigation-menu": "^1.2.13",
                "@radix-ui\/react-select": "^2.2.5",
                "@radix-ui\/react-separator": "^1.1.7",
                "@radix-ui\/react-slot": "^1.2.3",
                "@radix-ui\/react-tabs": "^1.1.12",
                "@radix-ui\/react-toast": "^1.2.14",
                "@radix-ui\/react-tooltip": "^1.2.7",
                "axios": "^1.10.0",
                "class-variance-authority": "^0.7.1",
                "clsx": "^2.1.1",
                "lucide-react": "^0.523.0",
                "react": "^18.2.0",
                "react-dom": "^18.2.0",
                "react-router-dom": "^7.6.2",
                "recharts": "^3.0.0",
                "sweetalert2": "^11.22.2",
                "tailwind-merge": "^3.3.1"
            },
            "devDependencies": {
                "@tailwindcss\/aspect-ratio": "^0.4.2",
                "@tailwindcss\/forms": "^0.5.10",
                "@tailwindcss\/typography": "^0.5.16",
                "@vitejs\/plugin-react": "^4.0.0",
                "autoprefixer": "^10.4.14",
                "laravel-vite-plugin": "^0.8.1",
                "postcss": "^8.4.24",
                "tailwindcss": "^3.3.2",
                "tailwindcss-animate": "^1.0.7",
                "vite": "^4.3.9"
            }
        },
        "framework": "Laravel",
        "php_version": "8.2.12",
        "indexed_at": "2025-07-10 06:56:47"
    },
    "models": {
        "CommissionAgent": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/CommissionAgent.php",
            "class_name": "CommissionAgent",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Model"
            ],
            "fillable": [],
            "relationships": [],
            "casts": [],
            "scopes": [],
            "methods": []
        },
        "Country": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/Country.php",
            "class_name": "Country",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Model"
            ],
            "fillable": [
                "name",
                "code",
                "iso_code",
                "capital",
                "currency",
                "phone_code",
                "continent",
                "population",
                "status"
            ],
            "relationships": [],
            "casts": {
                "status": "string",
                "created_at": "datetime",
                "updated_at": "datetime"
            },
            "scopes": [
                "Active",
                "Inactive",
                "ByContinent"
            ],
            "methods": {
                "scopeActive": "public",
                "scopeInactive": "public",
                "scopeByContinent": "public",
                "getFormattedPopulationAttribute": "public",
                "getFullNameAttribute": "public"
            }
        },
        "Currency": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/Currency.php",
            "class_name": "Currency",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Factories\\HasFactory"
            ],
            "fillable": [
                "name",
                "code",
                "symbol",
                "exchange_rate",
                "is_default",
                "is_active"
            ],
            "relationships": [],
            "casts": {
                "exchange_rate": "decimal:4",
                "is_default": "boolean",
                "is_active": "boolean"
            },
            "scopes": [
                "Active",
                "Default"
            ],
            "methods": {
                "scopeActive": "public",
                "scopeDefault": "public",
                "getDisplayNameAttribute": "public"
            }
        },
        "LandAcquisition": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/LandAcquisition.php",
            "class_name": "LandAcquisition",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Factories\\HasFactory"
            ],
            "fillable": [
                "record_dag",
                "khatian",
                "mauza",
                "land_size",
                "acquisition_price",
                "landOwners_id"
            ],
            "relationships": {
                "landOwner": "belongsTo",
                "landDocuments": "hasMany",
                "landAddress": "hasOne"
            },
            "casts": {
                "land_size": "decimal:2",
                "acquisition_price": "decimal:2",
                "landOwners_id": "integer"
            },
            "scopes": [
                "ByMauza",
                "ByKhatian",
                "ByRecordDag",
                "ByPriceRange",
                "ByLandSizeRange"
            ],
            "methods": {
                "landOwner": "public",
                "landDocuments": "public",
                "landAddress": "public",
                "scopeByMauza": "public",
                "scopeByKhatian": "public",
                "scopeByRecordDag": "public",
                "scopeByPriceRange": "public",
                "scopeByLandSizeRange": "public"
            }
        },
        "LandAddress": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/LandAddress.php",
            "class_name": "LandAddress",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Model"
            ],
            "fillable": [
                "land_acquisition_id",
                "plot_no",
                "road",
                "area",
                "upazila",
                "thana",
                "city",
                "district",
                "country",
                "zip_code"
            ],
            "relationships": {
                "landAcquisition": "belongsTo"
            },
            "casts": [],
            "scopes": [],
            "methods": {
                "landAcquisition": "public"
            }
        },
        "LandDocument": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/LandDocument.php",
            "class_name": "LandDocument",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Factories\\HasFactory"
            ],
            "fillable": [
                "land_acquisition_id",
                "document_name",
                "document_file_path",
                "original_filename",
                "file_type",
                "file_size",
                "description",
                "uploaded_by"
            ],
            "relationships": {
                "landAcquisition": "belongsTo",
                "uploadedBy": "belongsTo"
            },
            "casts": {
                "land_acquisition_id": "integer",
                "file_size": "integer",
                "uploaded_by": "integer"
            },
            "scopes": [],
            "methods": {
                "landAcquisition": "public",
                "uploadedBy": "public",
                "getFormattedFileSizeAttribute": "public",
                "getFileUrlAttribute": "public"
            }
        },
        "LandOwner": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/LandOwner.php",
            "class_name": "LandOwner",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Factories\\HasFactory"
            ],
            "fillable": [
                "first_name",
                "last_name",
                "father_name",
                "mother_name",
                "address",
                "phone",
                "nid_number",
                "email",
                "photo",
                "document_type",
                "nid_front",
                "nid_back",
                "passport_photo",
                "created_by",
                "updated_by",
                "status"
            ],
            "relationships": {
                "landAcquisitions": "hasMany",
                "creator": "belongsTo",
                "updater": "belongsTo",
                "audits": "hasMany",
                "getFullNameAttribute": "audits"
            },
            "casts": [],
            "scopes": [],
            "methods": {
                "landAcquisitions": "public",
                "creator": "public",
                "updater": "public",
                "audits": "public",
                "getFullNameAttribute": "public",
                "getNameAttribute": "public",
                "getLatestAuditAttribute": "public",
                "getCreationInfoAttribute": "public",
                "getUpdateInfoAttribute": "public",
                "createAudit": "public"
            }
        },
        "LandOwnerAudit": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/LandOwnerAudit.php",
            "class_name": "LandOwnerAudit",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Factories\\HasFactory"
            ],
            "fillable": [
                "land_owner_id",
                "user_id",
                "action",
                "event_type",
                "user_name",
                "user_email",
                "old_values",
                "new_values",
                "changed_fields",
                "ip_address",
                "user_agent",
                "source",
                "description",
                "metadata"
            ],
            "relationships": {
                "landOwner": "belongsTo",
                "user": "belongsTo"
            },
            "casts": {
                "old_values": "array",
                "new_values": "array",
                "changed_fields": "array",
                "metadata": "array"
            },
            "scopes": [],
            "methods": {
                "landOwner": "public",
                "user": "public",
                "getActionTextAttribute": "public",
                "getFormattedDateAttribute": "public",
                "getChangesDescriptionAttribute": "public"
            }
        },
        "Language": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/Language.php",
            "class_name": "Language",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Model"
            ],
            "fillable": [
                "name",
                "code",
                "native_name",
                "flag",
                "direction",
                "status",
                "is_default"
            ],
            "relationships": [],
            "casts": {
                "status": "string",
                "direction": "string",
                "is_default": "boolean",
                "created_at": "datetime",
                "updated_at": "datetime"
            },
            "scopes": [
                "Active",
                "Inactive",
                "Default"
            ],
            "methods": {
                "scopeActive": "public",
                "scopeInactive": "public",
                "scopeDefault": "public",
                "getFullNameAttribute": "public",
                "getDisplayNameAttribute": "public"
            }
        },
        "Role": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/Role.php",
            "class_name": "Role",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Database\\Eloquent\\Model"
            ],
            "fillable": [
                "name",
                "description",
                "accessible_modules",
                "module_permissions",
                "status",
                "users_count"
            ],
            "relationships": {
                "getFormattedCreatedAtAttribute": "hasMany"
            },
            "casts": {
                "accessible_modules": "array",
                "module_permissions": "array",
                "created_at": "datetime",
                "updated_at": "datetime"
            },
            "scopes": [
                "Active",
                "Inactive"
            ],
            "methods": {
                "scopeActive": "public",
                "scopeInactive": "public",
                "getFormattedCreatedAtAttribute": "public",
                "getFormattedUpdatedAtAttribute": "public",
                "hasModuleAccess": "public",
                "getModulePermissions": "public",
                "hasPermission": "public",
                "users": "public"
            }
        },
        "User": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Models\/User.php",
            "class_name": "User",
            "namespace": "App\\Models",
            "traits": [
                "Illuminate\\Contracts\\Auth\\MustVerifyEmail"
            ],
            "fillable": [
                "name",
                "first_name",
                "last_name",
                "email",
                "password",
                "phone",
                "company",
                "bio",
                "timezone",
                "language",
                "role_id"
            ],
            "relationships": {
                "role": "belongsTo"
            },
            "casts": [],
            "scopes": [],
            "methods": {
                "role": "public",
                "hasModuleAccess": "public",
                "hasPermission": "public",
                "getAccessibleModulesAttribute": "public",
                "getPermissionsAttribute": "public",
                "setFirstNameAttribute": "public",
                "setLastNameAttribute": "public"
            }
        }
    },
    "controllers": {
        "AuthController": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/AuthController.php",
            "class_name": "AuthController",
            "namespace": "App\\Http\\Controllers",
            "traits": [
                "App\\Models\\User"
            ],
            "methods": {
                "register": "JsonResponse",
                "login": "JsonResponse",
                "logout": "JsonResponse",
                "logoutAll": "JsonResponse",
                "profile": "JsonResponse",
                "updateProfile": "JsonResponse",
                "changePassword": "JsonResponse",
                "permissions": "JsonResponse",
                "refreshToken": "JsonResponse"
            },
            "middleware": [],
            "dependencies": [
                "App\\Models\\User",
                "App\\Models\\Role",
                "App\\Traits\\EnvironmentAwareTrait",
                "Illuminate\\Http\\Request",
                "Illuminate\\Http\\JsonResponse",
                "Illuminate\\Support\\Facades\\Hash",
                "Illuminate\\Support\\Facades\\Auth",
                "Illuminate\\Support\\Facades\\Validator",
                "Illuminate\\Validation\\ValidationException",
                "Laravel\\Sanctum\\HasApiTokens",
                "EnvironmentAwareTrait"
            ]
        },
        "Controller": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/Controller.php",
            "class_name": "Controller",
            "namespace": "App\\Http\\Controllers",
            "traits": [],
            "methods": [],
            "middleware": [],
            "dependencies": []
        },
        "CountryController": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/CountryController.php",
            "class_name": "CountryController",
            "namespace": "App\\Http\\Controllers",
            "traits": [
                "App\\Models\\Country"
            ],
            "methods": {
                "index": "JsonResponse",
                "store": "JsonResponse",
                "show": "JsonResponse",
                "update": "JsonResponse",
                "destroy": "JsonResponse",
                "getStatistics": "JsonResponse",
                "getContinents": "JsonResponse",
                "toggleStatus": "JsonResponse"
            },
            "middleware": [],
            "dependencies": [
                "App\\Models\\Country",
                "Illuminate\\Http\\Request",
                "Illuminate\\Http\\JsonResponse",
                "Illuminate\\Validation\\Rule",
                "Illuminate\\Support\\Facades\\DB",
                "($search) {\n                    $q->where('name', 'LIKE', \"%{$search}%\")\n                      ->orWhere('code', 'LIKE', \"%{$search}%\")\n                      ->orWhere('iso_code', 'LIKE', \"%{$search}%\")\n                      ->orWhere('capital', 'LIKE', \"%{$search}%\")\n                      ->orWhere('continent', 'LIKE', \"%{$search}%\")\n                      ->orWhere('currency', 'LIKE', \"%{$search}%\")"
            ]
        },
        "CurrencyController": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/CurrencyController.php",
            "class_name": "CurrencyController",
            "namespace": "App\\Http\\Controllers",
            "traits": [
                "App\\Models\\Currency"
            ],
            "methods": {
                "index": "JsonResponse",
                "store": "JsonResponse",
                "show": "JsonResponse",
                "update": "JsonResponse",
                "destroy": "JsonResponse",
                "getStatistics": "JsonResponse",
                "toggleStatus": "JsonResponse"
            },
            "middleware": [],
            "dependencies": [
                "App\\Models\\Currency",
                "Illuminate\\Http\\Request",
                "Illuminate\\Http\\JsonResponse",
                "Illuminate\\Support\\Facades\\Validator",
                "($search) {\n                    $q->where('name', 'like', \"%{$search}%\")\n                      ->orWhere('code', 'like', \"%{$search}%\")\n                      ->orWhere('symbol', 'like', \"%{$search}%\")"
            ]
        },
        "LandAcquisitionController": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/LandAcquisitionController.php",
            "class_name": "LandAcquisitionController",
            "namespace": "App\\Http\\Controllers",
            "traits": [
                "App\\Models\\LandAcquisition"
            ],
            "methods": {
                "index": "JsonResponse",
                "store": "JsonResponse",
                "show": "JsonResponse",
                "update": "JsonResponse",
                "destroy": "JsonResponse",
                "statistics": "JsonResponse"
            },
            "middleware": [
                "auth:sanctum"
            ],
            "dependencies": [
                "App\\Models\\LandAcquisition",
                "App\\Models\\LandOwner",
                "App\\Models\\LandAddress",
                "Illuminate\\Http\\Request",
                "Illuminate\\Http\\JsonResponse",
                "Illuminate\\Support\\Facades\\DB",
                "Illuminate\\Support\\Str",
                "($search) {\n                $q->where('record_dag', 'like', \"%{$search}%\")\n                  ->orWhere('khatian', 'like', \"%{$search}%\")\n                  ->orWhere('mauza', 'like', \"%{$search}%\")"
            ]
        },
        "LandDocumentController": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/LandDocumentController.php",
            "class_name": "LandDocumentController",
            "namespace": "App\\Http\\Controllers",
            "traits": [
                "App\\Models\\LandDocument"
            ],
            "methods": {
                "index": "JsonResponse",
                "store": "JsonResponse",
                "show": "JsonResponse",
                "update": "JsonResponse",
                "destroy": "JsonResponse"
            },
            "middleware": [],
            "dependencies": [
                "App\\Models\\LandDocument",
                "App\\Models\\LandAcquisition",
                "Illuminate\\Http\\Request",
                "Illuminate\\Http\\JsonResponse",
                "Illuminate\\Support\\Facades\\Auth",
                "Illuminate\\Support\\Facades\\Storage",
                "Illuminate\\Support\\Str"
            ]
        },
        "LandOwnerController": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/LandOwnerController.php",
            "class_name": "LandOwnerController",
            "namespace": "App\\Http\\Controllers",
            "traits": [
                "App\\Models\\LandOwner"
            ],
            "methods": {
                "index": "JsonResponse",
                "store": "JsonResponse",
                "show": "JsonResponse",
                "update": "JsonResponse",
                "destroy": "JsonResponse",
                "dropdown": "JsonResponse",
                "auditHistory": "JsonResponse",
                "detailedAuditHistory": "JsonResponse",
                "allAudits": "JsonResponse",
                "auditStats": "JsonResponse",
                "testIndex": "JsonResponse",
                "activate": "JsonResponse",
                "deactivate": "JsonResponse"
            },
            "middleware": [
                "auth:sanctum"
            ],
            "dependencies": [
                "App\\Models\\LandOwner",
                "App\\Models\\LandOwnerAudit",
                "App\\Traits\\EnvironmentAwareTrait",
                "Illuminate\\Http\\Request",
                "Illuminate\\Http\\JsonResponse",
                "Illuminate\\Support\\Facades\\Storage",
                "Illuminate\\Support\\Facades\\Auth",
                "Illuminate\\Support\\Str",
                "EnvironmentAwareTrait",
                "($search) {\n                $q->where('first_name', 'like', \"%{$search}%\")\n                  ->orWhere('last_name', 'like', \"%{$search}%\")\n                  ->orWhere('father_name', 'like', \"%{$search}%\")\n                  ->orWhere('phone', 'like', \"%{$search}%\")\n                  ->orWhere('nid_number', 'like', \"%{$search}%\")"
            ]
        },
        "LanguageController": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/LanguageController.php",
            "class_name": "LanguageController",
            "namespace": "App\\Http\\Controllers",
            "traits": [
                "App\\Models\\Language"
            ],
            "methods": {
                "index": "JsonResponse",
                "store": "JsonResponse",
                "show": "JsonResponse",
                "update": "JsonResponse",
                "destroy": "JsonResponse",
                "setDefault": "JsonResponse",
                "getStatistics": "JsonResponse"
            },
            "middleware": [],
            "dependencies": [
                "App\\Models\\Language",
                "Illuminate\\Http\\Request",
                "Illuminate\\Http\\JsonResponse",
                "Illuminate\\Validation\\Rule",
                "($search) {\n                    $q->where('name', 'LIKE', \"%{$search}%\")\n                      ->orWhere('code', 'LIKE', \"%{$search}%\")\n                      ->orWhere('native_name', 'LIKE', \"%{$search}%\")"
            ]
        },
        "ProductionDiagnosticsController": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/ProductionDiagnosticsController.php",
            "class_name": "ProductionDiagnosticsController",
            "namespace": "App\\Http\\Controllers",
            "traits": [
                "Illuminate\\Http\\Request"
            ],
            "methods": {
                "diagnose": "JsonResponse"
            },
            "middleware": [],
            "dependencies": [
                "Illuminate\\Http\\Request",
                "Illuminate\\Http\\JsonResponse"
            ]
        },
        "RoleController": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Controllers\/RoleController.php",
            "class_name": "RoleController",
            "namespace": "App\\Http\\Controllers",
            "traits": [
                "App\\Models\\Role"
            ],
            "methods": {
                "index": "JsonResponse",
                "store": "JsonResponse",
                "show": "JsonResponse",
                "update": "JsonResponse",
                "destroy": "JsonResponse",
                "getAvailableModules": "JsonResponse",
                "getStatistics": "JsonResponse",
                "bulkUpdateStatus": "JsonResponse"
            },
            "middleware": [],
            "dependencies": [
                "App\\Models\\Role",
                "Illuminate\\Http\\Request",
                "Illuminate\\Http\\JsonResponse",
                "Illuminate\\Validation\\Rule",
                "Illuminate\\Support\\Facades\\DB",
                "($search) {\n                    $q->where('name', 'LIKE', \"%{$search}%\")\n                      ->orWhere('description', 'LIKE', \"%{$search}%\")"
            ]
        }
    },
    "routes": {
        "api": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/routes\/api.php",
            "routes": [
                {
                    "method": "POST",
                    "uri": "register",
                    "controller": "AuthController::class, 'register'"
                },
                {
                    "method": "POST",
                    "uri": "login",
                    "controller": "AuthController::class, 'login'"
                },
                {
                    "method": "POST",
                    "uri": "logout",
                    "controller": "AuthController::class, 'logout'"
                },
                {
                    "method": "POST",
                    "uri": "logout-all",
                    "controller": "AuthController::class, 'logoutAll'"
                },
                {
                    "method": "GET",
                    "uri": "profile",
                    "controller": "AuthController::class, 'profile'"
                },
                {
                    "method": "PUT",
                    "uri": "profile",
                    "controller": "AuthController::class, 'updateProfile'"
                },
                {
                    "method": "POST",
                    "uri": "change-password",
                    "controller": "AuthController::class, 'changePassword'"
                },
                {
                    "method": "GET",
                    "uri": "permissions",
                    "controller": "AuthController::class, 'permissions'"
                },
                {
                    "method": "POST",
                    "uri": "refresh-token",
                    "controller": "AuthController::class, 'refreshToken'"
                },
                {
                    "method": "GET",
                    "uri": "land-acquisitions",
                    "controller": "LandAcquisitionController::class, 'index'"
                },
                {
                    "method": "GET",
                    "uri": "land-acquisitions\/{landAcquisition}",
                    "controller": "LandAcquisitionController::class, 'show'"
                },
                {
                    "method": "GET",
                    "uri": "land-acquisitions-statistics",
                    "controller": "LandAcquisitionController::class, 'statistics'"
                },
                {
                    "method": "POST",
                    "uri": "land-acquisitions",
                    "controller": "LandAcquisitionController::class, 'store'"
                },
                {
                    "method": "PUT",
                    "uri": "land-acquisitions\/{landAcquisition}",
                    "controller": "LandAcquisitionController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "land-acquisitions\/{landAcquisition}",
                    "controller": "LandAcquisitionController::class, 'update'"
                },
                {
                    "method": "POST",
                    "uri": "land-acquisitions\/{landAcquisition}",
                    "controller": "LandAcquisitionController::class, 'update'"
                },
                {
                    "method": "DELETE",
                    "uri": "land-acquisitions\/{landAcquisition}",
                    "controller": "LandAcquisitionController::class, 'destroy'"
                },
                {
                    "method": "GET",
                    "uri": "roles",
                    "controller": "RoleController::class, 'index'"
                },
                {
                    "method": "GET",
                    "uri": "roles\/{role}",
                    "controller": "RoleController::class, 'show'"
                },
                {
                    "method": "GET",
                    "uri": "roles-statistics",
                    "controller": "RoleController::class, 'getStatistics'"
                },
                {
                    "method": "GET",
                    "uri": "roles-modules",
                    "controller": "RoleController::class, 'getAvailableModules'"
                },
                {
                    "method": "POST",
                    "uri": "roles",
                    "controller": "RoleController::class, 'store'"
                },
                {
                    "method": "PUT",
                    "uri": "roles\/{role}",
                    "controller": "RoleController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "roles\/{role}",
                    "controller": "RoleController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "roles-bulk-status",
                    "controller": "RoleController::class, 'bulkUpdateStatus'"
                },
                {
                    "method": "DELETE",
                    "uri": "roles\/{role}",
                    "controller": "RoleController::class, 'destroy'"
                },
                {
                    "method": "GET",
                    "uri": "land-owners",
                    "controller": "LandOwnerController::class, 'index'"
                },
                {
                    "method": "GET",
                    "uri": "land-owners\/{landOwner}",
                    "controller": "LandOwnerController::class, 'show'"
                },
                {
                    "method": "GET",
                    "uri": "land-owners-dropdown",
                    "controller": "LandOwnerController::class, 'dropdown'"
                },
                {
                    "method": "GET",
                    "uri": "land-owners\/{id}\/audit-history",
                    "controller": "LandOwnerController::class, 'auditHistory'"
                },
                {
                    "method": "GET",
                    "uri": "land-owners\/{id}\/detailed-audit-history",
                    "controller": "LandOwnerController::class, 'detailedAuditHistory'"
                },
                {
                    "method": "GET",
                    "uri": "land-owners-audits",
                    "controller": "LandOwnerController::class, 'allAudits'"
                },
                {
                    "method": "GET",
                    "uri": "land-owners-audit-stats",
                    "controller": "LandOwnerController::class, 'auditStats'"
                },
                {
                    "method": "POST",
                    "uri": "land-owners",
                    "controller": "LandOwnerController::class, 'store'"
                },
                {
                    "method": "PUT",
                    "uri": "land-owners\/{landOwner}",
                    "controller": "LandOwnerController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "land-owners\/{landOwner}",
                    "controller": "LandOwnerController::class, 'update'"
                },
                {
                    "method": "POST",
                    "uri": "land-owners\/{landOwner}",
                    "controller": "LandOwnerController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "land-owners\/{landOwner}\/activate",
                    "controller": "LandOwnerController::class, 'activate'"
                },
                {
                    "method": "PATCH",
                    "uri": "land-owners\/{landOwner}\/deactivate",
                    "controller": "LandOwnerController::class, 'deactivate'"
                },
                {
                    "method": "DELETE",
                    "uri": "land-owners\/{landOwner}",
                    "controller": "LandOwnerController::class, 'destroy'"
                },
                {
                    "method": "GET",
                    "uri": "land-documents",
                    "controller": "LandDocumentController::class, 'index'"
                },
                {
                    "method": "GET",
                    "uri": "land-documents\/{landDocument}",
                    "controller": "LandDocumentController::class, 'show'"
                },
                {
                    "method": "GET",
                    "uri": "land-documents\/{landDocument}\/download",
                    "controller": "LandDocumentController::class, 'download'"
                },
                {
                    "method": "POST",
                    "uri": "land-documents",
                    "controller": "LandDocumentController::class, 'store'"
                },
                {
                    "method": "PUT",
                    "uri": "land-documents\/{landDocument}",
                    "controller": "LandDocumentController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "land-documents\/{landDocument}",
                    "controller": "LandDocumentController::class, 'update'"
                },
                {
                    "method": "POST",
                    "uri": "land-documents\/{landDocument}",
                    "controller": "LandDocumentController::class, 'update'"
                },
                {
                    "method": "DELETE",
                    "uri": "land-documents\/{landDocument}",
                    "controller": "LandDocumentController::class, 'destroy'"
                },
                {
                    "method": "GET",
                    "uri": "countries",
                    "controller": "CountryController::class, 'index'"
                },
                {
                    "method": "GET",
                    "uri": "countries\/{country}",
                    "controller": "CountryController::class, 'show'"
                },
                {
                    "method": "GET",
                    "uri": "countries-statistics",
                    "controller": "CountryController::class, 'getStatistics'"
                },
                {
                    "method": "GET",
                    "uri": "countries-continents",
                    "controller": "CountryController::class, 'getContinents'"
                },
                {
                    "method": "POST",
                    "uri": "countries",
                    "controller": "CountryController::class, 'store'"
                },
                {
                    "method": "PUT",
                    "uri": "countries\/{country}",
                    "controller": "CountryController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "countries\/{country}",
                    "controller": "CountryController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "countries\/{country}\/toggle-status",
                    "controller": "CountryController::class, 'toggleStatus'"
                },
                {
                    "method": "DELETE",
                    "uri": "countries\/{country}",
                    "controller": "CountryController::class, 'destroy'"
                },
                {
                    "method": "GET",
                    "uri": "languages",
                    "controller": "LanguageController::class, 'index'"
                },
                {
                    "method": "GET",
                    "uri": "languages\/{language}",
                    "controller": "LanguageController::class, 'show'"
                },
                {
                    "method": "GET",
                    "uri": "languages-statistics",
                    "controller": "LanguageController::class, 'getStatistics'"
                },
                {
                    "method": "POST",
                    "uri": "languages",
                    "controller": "LanguageController::class, 'store'"
                },
                {
                    "method": "PUT",
                    "uri": "languages\/{language}",
                    "controller": "LanguageController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "languages\/{language}",
                    "controller": "LanguageController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "languages\/{language}\/set-default",
                    "controller": "LanguageController::class, 'setDefault'"
                },
                {
                    "method": "DELETE",
                    "uri": "languages\/{language}",
                    "controller": "LanguageController::class, 'destroy'"
                },
                {
                    "method": "GET",
                    "uri": "currencies",
                    "controller": "CurrencyController::class, 'index'"
                },
                {
                    "method": "GET",
                    "uri": "currencies\/{currency}",
                    "controller": "CurrencyController::class, 'show'"
                },
                {
                    "method": "GET",
                    "uri": "currencies-statistics",
                    "controller": "CurrencyController::class, 'getStatistics'"
                },
                {
                    "method": "POST",
                    "uri": "currencies",
                    "controller": "CurrencyController::class, 'store'"
                },
                {
                    "method": "PUT",
                    "uri": "currencies\/{currency}",
                    "controller": "CurrencyController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "currencies\/{currency}",
                    "controller": "CurrencyController::class, 'update'"
                },
                {
                    "method": "PATCH",
                    "uri": "currencies\/{currency}\/toggle-status",
                    "controller": "CurrencyController::class, 'toggleStatus'"
                },
                {
                    "method": "DELETE",
                    "uri": "currencies\/{currency}",
                    "controller": "CurrencyController::class, 'destroy'"
                }
            ],
            "middleware_groups": {
                "0": "auth:sanctum",
                "2": "permission:land-acquisition,read",
                "3": "permission:land-acquisition,create",
                "4": "permission:land-acquisition,update",
                "5": "permission:land-acquisition,delete",
                "6": "permission:role,read",
                "7": "permission:role,create",
                "8": "permission:role,update",
                "9": "permission:role,delete",
                "10": "permission:land-owners,read",
                "11": "permission:land-owners,create",
                "12": "permission:land-owners,update",
                "13": "permission:land-owners,delete",
                "18": "permission:country,read",
                "19": "permission:country,create",
                "20": "permission:country,update",
                "21": "permission:country,delete",
                "22": "permission:language,read",
                "23": "permission:language,create",
                "24": "permission:language,update",
                "25": "permission:language,delete",
                "26": "permission:currency,read",
                "27": "permission:currency,create",
                "28": "permission:currency,update",
                "29": "permission:currency,delete"
            },
            "controllers_used": {
                "0": "AuthController",
                "9": "LandAcquisitionController",
                "17": "RoleController",
                "26": "LandOwnerController",
                "40": "LandDocumentController",
                "48": "CountryController",
                "57": "LanguageController",
                "65": "CurrencyController"
            }
        },
        "web": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/routes\/web.php",
            "routes": [],
            "middleware_groups": [],
            "controllers_used": []
        },
        "debug": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/routes\/debug.php",
            "routes": [],
            "middleware_groups": [],
            "controllers_used": []
        },
        "console": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/routes\/console.php",
            "routes": [],
            "middleware_groups": [],
            "controllers_used": []
        }
    },
    "migrations": {
        "0001_01_01_000000_create_users_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/0001_01_01_000000_create_users_table.php",
            "filename": "0001_01_01_000000_create_users_table",
            "table_name": "users",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "string",
                    "name": "email"
                },
                {
                    "type": "timestamp",
                    "name": "email_verified_at"
                },
                {
                    "type": "string",
                    "name": "password"
                },
                {
                    "type": "string",
                    "name": "email"
                },
                {
                    "type": "string",
                    "name": "token"
                },
                {
                    "type": "timestamp",
                    "name": "created_at"
                },
                {
                    "type": "string",
                    "name": "id"
                },
                {
                    "type": "foreignId",
                    "name": "user_id"
                },
                {
                    "type": "string",
                    "name": "ip_address"
                },
                {
                    "type": "text",
                    "name": "user_agent"
                },
                {
                    "type": "longText",
                    "name": "payload"
                },
                {
                    "type": "integer",
                    "name": "last_activity"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "0001_01_01_000001_create_cache_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/0001_01_01_000001_create_cache_table.php",
            "filename": "0001_01_01_000001_create_cache_table",
            "table_name": "cache",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "key"
                },
                {
                    "type": "mediumText",
                    "name": "value"
                },
                {
                    "type": "integer",
                    "name": "expiration"
                },
                {
                    "type": "string",
                    "name": "key"
                },
                {
                    "type": "string",
                    "name": "owner"
                },
                {
                    "type": "integer",
                    "name": "expiration"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "0001_01_01_000002_create_jobs_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/0001_01_01_000002_create_jobs_table.php",
            "filename": "0001_01_01_000002_create_jobs_table",
            "table_name": "jobs",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "queue"
                },
                {
                    "type": "longText",
                    "name": "payload"
                },
                {
                    "type": "unsignedTinyInteger",
                    "name": "attempts"
                },
                {
                    "type": "unsignedInteger",
                    "name": "reserved_at"
                },
                {
                    "type": "unsignedInteger",
                    "name": "available_at"
                },
                {
                    "type": "unsignedInteger",
                    "name": "created_at"
                },
                {
                    "type": "string",
                    "name": "id"
                },
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "integer",
                    "name": "total_jobs"
                },
                {
                    "type": "integer",
                    "name": "pending_jobs"
                },
                {
                    "type": "integer",
                    "name": "failed_jobs"
                },
                {
                    "type": "longText",
                    "name": "failed_job_ids"
                },
                {
                    "type": "mediumText",
                    "name": "options"
                },
                {
                    "type": "integer",
                    "name": "cancelled_at"
                },
                {
                    "type": "integer",
                    "name": "created_at"
                },
                {
                    "type": "integer",
                    "name": "finished_at"
                },
                {
                    "type": "string",
                    "name": "uuid"
                },
                {
                    "type": "text",
                    "name": "connection"
                },
                {
                    "type": "text",
                    "name": "queue"
                },
                {
                    "type": "longText",
                    "name": "payload"
                },
                {
                    "type": "longText",
                    "name": "exception"
                },
                {
                    "type": "timestamp",
                    "name": "failed_at"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "2024_12_24_000000_create_land_owners_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2024_12_24_000000_create_land_owners_table.php",
            "filename": "2024_12_24_000000_create_land_owners_table",
            "table_name": "land_owners",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "string",
                    "name": "father_name"
                },
                {
                    "type": "string",
                    "name": "mother_name"
                },
                {
                    "type": "text",
                    "name": "address"
                },
                {
                    "type": "string",
                    "name": "phone"
                },
                {
                    "type": "string",
                    "name": "nid_number"
                },
                {
                    "type": "string",
                    "name": "email"
                },
                {
                    "type": "string",
                    "name": "photo"
                },
                {
                    "type": "decimal",
                    "name": "ownership_percentage"
                },
                {
                    "type": "decimal",
                    "name": "cash_received"
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                }
            ],
            "indexes": [
                "name",
                "nid_number"
            ],
            "foreign_keys": []
        },
        "2024_12_25_000000_create_land_acquisitions_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2024_12_25_000000_create_land_acquisitions_table.php",
            "filename": "2024_12_25_000000_create_land_acquisitions_table",
            "table_name": "land_acquisitions",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "record_dag"
                },
                {
                    "type": "string",
                    "name": "khatian"
                },
                {
                    "type": "string",
                    "name": "mauza"
                },
                {
                    "type": "decimal",
                    "name": "land_size"
                },
                {
                    "type": "decimal",
                    "name": "acquisition_price"
                },
                {
                    "type": "unsignedBigInteger",
                    "name": "landOwners_id"
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "foreign",
                    "name": "landOwners_id"
                }
            ],
            "indexes": [
                "record_dag",
                "khatian",
                "mauza",
                "landOwners_id"
            ],
            "foreign_keys": [
                {
                    "column": "landOwners_id",
                    "references": "id",
                    "on": "land_owners"
                }
            ]
        },
        "2025_06_26_091133_create_commission_agents_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_06_26_091133_create_commission_agents_table.php",
            "filename": "2025_06_26_091133_create_commission_agents_table",
            "table_name": "commission_agents",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "string",
                    "name": "phone"
                },
                {
                    "type": "string",
                    "name": "email"
                },
                {
                    "type": "string",
                    "name": "address"
                },
                {
                    "type": "string",
                    "name": "fathers_name"
                },
                {
                    "type": "string",
                    "name": "mothers_name"
                },
                {
                    "type": "string",
                    "name": "nid"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_06_27_083955_create_roles_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_06_27_083955_create_roles_table.php",
            "filename": "2025_06_27_083955_create_roles_table",
            "table_name": "roles",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "text",
                    "name": "description"
                },
                {
                    "type": "json",
                    "name": "accessible_modules"
                },
                {
                    "type": "json",
                    "name": "module_permissions"
                },
                {
                    "type": "enum",
                    "name": "status"
                },
                {
                    "type": "integer",
                    "name": "users_count"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_06_30_044928_add_role_id_to_users_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_06_30_044928_add_role_id_to_users_table.php",
            "filename": "2025_06_30_044928_add_role_id_to_users_table",
            "table_name": "users",
            "type": "add_column",
            "columns": [
                {
                    "type": "unsignedBigInteger",
                    "name": "role_id"
                },
                {
                    "type": "foreign",
                    "name": "role_id"
                },
                {
                    "type": "dropForeign",
                    "name": "["
                },
                {
                    "type": "dropColumn",
                    "name": "role_id"
                }
            ],
            "indexes": [],
            "foreign_keys": [
                {
                    "column": "role_id",
                    "references": "id",
                    "on": "roles"
                }
            ]
        },
        "2025_06_30_050321_create_personal_access_tokens_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_06_30_050321_create_personal_access_tokens_table.php",
            "filename": "2025_06_30_050321_create_personal_access_tokens_table",
            "table_name": "personal_access_tokens",
            "type": "create",
            "columns": [
                {
                    "type": "morphs",
                    "name": "tokenable"
                },
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "string",
                    "name": "token"
                },
                {
                    "type": "text",
                    "name": "abilities"
                },
                {
                    "type": "timestamp",
                    "name": "last_used_at"
                },
                {
                    "type": "timestamp",
                    "name": "expires_at"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_07_02_000001_add_tracking_fields_to_land_owners_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_02_000001_add_tracking_fields_to_land_owners_table.php",
            "filename": "2025_07_02_000001_add_tracking_fields_to_land_owners_table",
            "table_name": "land_owners",
            "type": "add_column",
            "columns": [
                {
                    "type": "unsignedBigInteger",
                    "name": "created_by"
                },
                {
                    "type": "foreign",
                    "name": "created_by"
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "unsignedBigInteger",
                    "name": "updated_by"
                },
                {
                    "type": "foreign",
                    "name": "updated_by"
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "dropForeign",
                    "name": "["
                },
                {
                    "type": "dropColumn",
                    "name": "["
                },
                {
                    "type": "dropForeign",
                    "name": "["
                },
                {
                    "type": "dropColumn",
                    "name": "["
                }
            ],
            "indexes": [
                "created_by",
                "updated_by"
            ],
            "foreign_keys": [
                {
                    "column": "created_by",
                    "references": "id",
                    "on": "users"
                },
                {
                    "column": "updated_by",
                    "references": "id",
                    "on": "users"
                }
            ]
        },
        "2025_07_02_000002_create_land_owner_audits_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_02_000002_create_land_owner_audits_table.php",
            "filename": "2025_07_02_000002_create_land_owner_audits_table",
            "table_name": "land_owner_audits",
            "type": "create",
            "columns": [
                {
                    "type": "unsignedBigInteger",
                    "name": "land_owner_id"
                },
                {
                    "type": "unsignedBigInteger",
                    "name": "user_id"
                },
                {
                    "type": "string",
                    "name": "action"
                },
                {
                    "type": "json",
                    "name": "old_values"
                },
                {
                    "type": "json",
                    "name": "new_values"
                },
                {
                    "type": "json",
                    "name": "changed_fields"
                },
                {
                    "type": "string",
                    "name": "ip_address"
                },
                {
                    "type": "text",
                    "name": "user_agent"
                },
                {
                    "type": "text",
                    "name": "notes"
                },
                {
                    "type": "foreign",
                    "name": "land_owner_id"
                },
                {
                    "type": "foreign",
                    "name": "user_id"
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                }
            ],
            "indexes": [
                "land_owner_id",
                "user_id",
                "action",
                "created_at"
            ],
            "foreign_keys": [
                {
                    "column": "land_owner_id",
                    "references": "id",
                    "on": "land_owners"
                },
                {
                    "column": "user_id",
                    "references": "id",
                    "on": "users"
                }
            ]
        },
        "2025_07_03_000000_add_document_fields_to_land_owners_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_03_000000_add_document_fields_to_land_owners_table.php",
            "filename": "2025_07_03_000000_add_document_fields_to_land_owners_table",
            "table_name": "land_owners",
            "type": "add_column",
            "columns": [
                {
                    "type": "enum",
                    "name": "document_type"
                },
                {
                    "type": "string",
                    "name": "nid_front"
                },
                {
                    "type": "string",
                    "name": "nid_back"
                },
                {
                    "type": "string",
                    "name": "passport_photo"
                },
                {
                    "type": "dropColumn",
                    "name": "["
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_07_03_062518_create_land_documents_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_03_062518_create_land_documents_table.php",
            "filename": "2025_07_03_062518_create_land_documents_table",
            "table_name": "land_documents",
            "type": "create",
            "columns": [
                {
                    "type": "foreignId",
                    "name": "land_acquisition_id"
                },
                {
                    "type": "string",
                    "name": "document_name"
                },
                {
                    "type": "string",
                    "name": "document_file_path"
                },
                {
                    "type": "string",
                    "name": "original_filename"
                },
                {
                    "type": "string",
                    "name": "file_type"
                },
                {
                    "type": "integer",
                    "name": "file_size"
                },
                {
                    "type": "text",
                    "name": "description"
                },
                {
                    "type": "foreignId",
                    "name": "uploaded_by"
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                }
            ],
            "indexes": [
                "land_acquisition_id",
                "document_name"
            ],
            "foreign_keys": []
        },
        "2025_07_07_000000_update_land_owners_table_split_name": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_07_000000_update_land_owners_table_split_name.php",
            "filename": "2025_07_07_000000_update_land_owners_table_split_name",
            "table_name": "land_owners",
            "type": "update",
            "columns": [
                {
                    "type": "string",
                    "name": "first_name"
                },
                {
                    "type": "string",
                    "name": "last_name"
                },
                {
                    "type": "dropColumn",
                    "name": "name"
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "dropIndex",
                    "name": "["
                },
                {
                    "type": "dropIndex",
                    "name": "["
                },
                {
                    "type": "dropIndex",
                    "name": "["
                },
                {
                    "type": "dropColumn",
                    "name": "["
                }
            ],
            "indexes": [
                "first_name",
                "last_name",
                "first_name"
            ],
            "foreign_keys": []
        },
        "2025_07_07_094610_drop_name_column_from_land_owners_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_07_094610_drop_name_column_from_land_owners_table.php",
            "filename": "2025_07_07_094610_drop_name_column_from_land_owners_table",
            "table_name": "land_owners",
            "type": "modify",
            "columns": [
                {
                    "type": "dropColumn",
                    "name": "name"
                },
                {
                    "type": "string",
                    "name": "name"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_07_07_110905_create_land_addresses_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_07_110905_create_land_addresses_table.php",
            "filename": "2025_07_07_110905_create_land_addresses_table",
            "table_name": "land_addresses",
            "type": "create",
            "columns": [
                {
                    "type": "foreignId",
                    "name": "land_acquisition_id"
                },
                {
                    "type": "string",
                    "name": "plot_no"
                },
                {
                    "type": "string",
                    "name": "road"
                },
                {
                    "type": "string",
                    "name": "area"
                },
                {
                    "type": "string",
                    "name": "upazila"
                },
                {
                    "type": "string",
                    "name": "thana"
                },
                {
                    "type": "string",
                    "name": "city"
                },
                {
                    "type": "string",
                    "name": "district"
                },
                {
                    "type": "string",
                    "name": "country"
                },
                {
                    "type": "string",
                    "name": "zip_code"
                },
                {
                    "type": "index",
                    "name": "land_acquisition_id"
                }
            ],
            "indexes": [
                "land_acquisition_id"
            ],
            "foreign_keys": []
        },
        "2025_07_08_123200_remove_ownership_and_cash_fields_from_land_owners_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_08_123200_remove_ownership_and_cash_fields_from_land_owners_table.php",
            "filename": "2025_07_08_123200_remove_ownership_and_cash_fields_from_land_owners_table",
            "table_name": "land_owners",
            "type": "remove_column",
            "columns": [],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_07_09_080013_create_countries_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_09_080013_create_countries_table.php",
            "filename": "2025_07_09_080013_create_countries_table",
            "table_name": "countries",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "string",
                    "name": "code"
                },
                {
                    "type": "string",
                    "name": "iso_code"
                },
                {
                    "type": "string",
                    "name": "capital"
                },
                {
                    "type": "string",
                    "name": "currency"
                },
                {
                    "type": "string",
                    "name": "phone_code"
                },
                {
                    "type": "string",
                    "name": "continent"
                },
                {
                    "type": "string",
                    "name": "population"
                },
                {
                    "type": "enum",
                    "name": "status"
                },
                {
                    "type": "index",
                    "name": "["
                },
                {
                    "type": "index",
                    "name": "["
                }
            ],
            "indexes": [
                "status",
                "continent"
            ],
            "foreign_keys": []
        },
        "2025_07_09_092036_create_languages_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_09_092036_create_languages_table.php",
            "filename": "2025_07_09_092036_create_languages_table",
            "table_name": "languages",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "string",
                    "name": "code"
                },
                {
                    "type": "string",
                    "name": "native_name"
                },
                {
                    "type": "string",
                    "name": "flag"
                },
                {
                    "type": "enum",
                    "name": "direction"
                },
                {
                    "type": "enum",
                    "name": "status"
                },
                {
                    "type": "index",
                    "name": "["
                }
            ],
            "indexes": [
                "status"
            ],
            "foreign_keys": []
        },
        "2025_07_09_095557_update_users_table_add_profile_fields": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_09_095557_update_users_table_add_profile_fields.php",
            "filename": "2025_07_09_095557_update_users_table_add_profile_fields",
            "table_name": "users",
            "type": "add_column",
            "columns": [
                {
                    "type": "string",
                    "name": "first_name"
                },
                {
                    "type": "string",
                    "name": "last_name"
                },
                {
                    "type": "string",
                    "name": "phone"
                },
                {
                    "type": "string",
                    "name": "company"
                },
                {
                    "type": "text",
                    "name": "bio"
                },
                {
                    "type": "string",
                    "name": "timezone"
                },
                {
                    "type": "string",
                    "name": "language"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_07_09_095723_migrate_user_names_to_first_last_name": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_09_095723_migrate_user_names_to_first_last_name.php",
            "filename": "2025_07_09_095723_migrate_user_names_to_first_last_name",
            "table_name": "unknown",
            "type": "modify",
            "columns": [],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_07_09_100720_create_currencies_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_09_100720_create_currencies_table.php",
            "filename": "2025_07_09_100720_create_currencies_table",
            "table_name": "currencies",
            "type": "create",
            "columns": [
                {
                    "type": "string",
                    "name": "name"
                },
                {
                    "type": "string",
                    "name": "code"
                },
                {
                    "type": "string",
                    "name": "symbol"
                },
                {
                    "type": "decimal",
                    "name": "exchange_rate"
                },
                {
                    "type": "boolean",
                    "name": "is_default"
                },
                {
                    "type": "boolean",
                    "name": "is_active"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_07_09_103255_add_is_default_to_languages_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_09_103255_add_is_default_to_languages_table.php",
            "filename": "2025_07_09_103255_add_is_default_to_languages_table",
            "table_name": "languages",
            "type": "add_column",
            "columns": [
                {
                    "type": "boolean",
                    "name": "is_default"
                },
                {
                    "type": "dropColumn",
                    "name": "is_default"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        },
        "2025_07_09_123847_add_status_to_land_owners_table": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/migrations\/2025_07_09_123847_add_status_to_land_owners_table.php",
            "filename": "2025_07_09_123847_add_status_to_land_owners_table",
            "table_name": "land_owners",
            "type": "add_column",
            "columns": [
                {
                    "type": "enum",
                    "name": "status"
                },
                {
                    "type": "dropColumn",
                    "name": "status"
                }
            ],
            "indexes": [],
            "foreign_keys": []
        }
    },
    "seeders": {
        "CountrySeeder": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/seeders\/CountrySeeder.php",
            "class_name": "CountrySeeder",
            "models_seeded": [],
            "dependencies": [
                "Illuminate\\Database\\Console\\Seeds\\WithoutModelEvents",
                "Illuminate\\Database\\Seeder"
            ]
        },
        "DatabaseSeeder": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/seeders\/DatabaseSeeder.php",
            "class_name": "DatabaseSeeder",
            "models_seeded": [],
            "dependencies": [
                "App\\Models\\User",
                "Illuminate\\Database\\Console\\Seeds\\WithoutModelEvents",
                "Illuminate\\Database\\Seeder"
            ]
        },
        "LandAcquisitionSeeder": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/seeders\/LandAcquisitionSeeder.php",
            "class_name": "LandAcquisitionSeeder",
            "models_seeded": [
                "LandAcquisition"
            ],
            "dependencies": [
                "Illuminate\\Database\\Seeder",
                "App\\Models\\LandAcquisition",
                "App\\Models\\LandOwner"
            ]
        },
        "LandOwnerSeeder": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/seeders\/LandOwnerSeeder.php",
            "class_name": "LandOwnerSeeder",
            "models_seeded": [
                "LandOwner"
            ],
            "dependencies": [
                "Illuminate\\Database\\Seeder",
                "App\\Models\\LandOwner",
                "15, Road 7, Dhanmondi, Dhaka-1205',\n                'phone' => '+880171234567',\n                'nid_number' => '1234567890123',\n                'email' => '<EMAIL>',\n            ],\n            [\n                'name' => 'Fatima Begum',\n                'father_name' => 'Mohammad Ali',\n                'address' => 'House 25, Road 12, Gulshan-1, Dhaka-1212',\n                'phone' => '+880181234567',\n                'nid_number' => '2345678901234',\n                'email' => '<EMAIL>',\n            ],\n            [\n                'name' => 'Mohammad Hassan',\n                'father_name' => 'Abdul Majid',\n                'address' => 'House 35, Road 18, Banani, Dhaka-1213',\n                'phone' => '+880191234567',\n                'nid_number' => '3456789012345',\n                'email' => '<EMAIL>',\n            ],\n            [\n                'name' => 'Rashida Khatun',\n                'father_name' => 'Abdul Latif',\n                'address' => 'House 45, Road 22, Uttara, Dhaka-1230',\n                'phone' => '+880161234567',\n                'nid_number' => '4567890123456',\n                'email' => '<EMAIL>',\n            ],\n            [\n                'name' => 'Aminul Islam',\n                'father_name' => 'Rafiqul Islam',\n                'address' => 'House 55, Road 28, Mirpur, Dhaka-1216',\n                'phone' => '+880151234567',\n                'nid_number' => '5678901234567',\n                'email' => '<EMAIL>',\n            ],\n        ]"
            ]
        },
        "LanguageSeeder": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/seeders\/LanguageSeeder.php",
            "class_name": "LanguageSeeder",
            "models_seeded": [],
            "dependencies": [
                "Illuminate\\Database\\Seeder",
                "Illuminate\\Support\\Facades\\DB"
            ]
        },
        "RoleSeeder": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/seeders\/RoleSeeder.php",
            "class_name": "RoleSeeder",
            "models_seeded": [],
            "dependencies": [
                "App\\Models\\Role",
                "Illuminate\\Database\\Console\\Seeds\\WithoutModelEvents",
                "Illuminate\\Database\\Seeder"
            ]
        },
        "UserSeeder": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/database\/seeders\/UserSeeder.php",
            "class_name": "UserSeeder",
            "models_seeded": [
                "User"
            ],
            "dependencies": [
                "Illuminate\\Database\\Console\\Seeds\\WithoutModelEvents",
                "Illuminate\\Database\\Seeder",
                "App\\Models\\User",
                "App\\Models\\Role",
                "Illuminate\\Support\\Facades\\Hash"
            ]
        }
    },
    "middleware": {
        "CheckPermission": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Middleware\/CheckPermission.php",
            "class_name": "CheckPermission",
            "namespace": "App\\Http\\Middleware",
            "methods": {
                "handle": "public"
            }
        },
        "DynamicAppUrlMiddleware": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Middleware\/DynamicAppUrlMiddleware.php",
            "class_name": "DynamicAppUrlMiddleware",
            "namespace": "App\\Http\\Middleware",
            "methods": {
                "handle": "public"
            }
        },
        "ProductionApiMiddleware": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Middleware\/ProductionApiMiddleware.php",
            "class_name": "ProductionApiMiddleware",
            "namespace": "App\\Http\\Middleware",
            "methods": {
                "handle": "public"
            }
        },
        "ProductionCorsMiddleware": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/app\/Http\/Middleware\/ProductionCorsMiddleware.php",
            "class_name": "ProductionCorsMiddleware",
            "namespace": "App\\Http\\Middleware",
            "methods": {
                "handle": "public"
            }
        }
    },
    "config": {
        "app": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/app.php",
            "size": 4263
        },
        "auth": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/auth.php",
            "size": 4029
        },
        "cache": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/cache.php",
            "size": 3476
        },
        "cors": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/cors.php",
            "size": 1160
        },
        "database": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/database.php",
            "size": 6420
        },
        "filesystems": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/filesystems.php",
            "size": 2500
        },
        "logging": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/logging.php",
            "size": 4318
        },
        "mail": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/mail.php",
            "size": 3605
        },
        "queue": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/queue.php",
            "size": 3824
        },
        "sanctum": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/sanctum.php",
            "size": 3037
        },
        "services": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/services.php",
            "size": 1035
        },
        "session": {
            "file": "C:\\xampp\\htdocs\\real-estate-management\/config\/session.php",
            "size": 7844
        }
    },
    "frontend": {
        "resources_path": "C:\\xampp\\htdocs\\real-estate-management\/resources",
        "build_tool": "Vite",
        "vite_config": "C:\\xampp\\htdocs\\real-estate-management\/vite.config.js",
        "css_framework": "Tailwind CSS",
        "tailwind_config": "C:\\xampp\\htdocs\\real-estate-management\/tailwind.config.js"
    },
    "database_structure": []
}