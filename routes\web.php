<?php

use Illuminate\Support\Facades\Route;

// Login route for authentication redirects
Route::get('/login', function () {
    return response()->json([
        'message' => 'Please use the API endpoint /api/auth/login for authentication',
        'login_endpoint' => '/api/auth/login'
    ], 401);
})->name('login');

// Debug route to check user permissions
Route::get('/debug-user', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->with('role')->first();
    
    if (!$user) {
        return response()->json(['error' => 'User not found']);
    }
    
    return response()->json([
        'user' => [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'role_id' => $user->role_id,
        ],
        'role' => $user->role ? [
            'id' => $user->role->id,
            'name' => $user->role->name,
            'accessible_modules' => $user->role->accessible_modules,
            'module_permissions' => $user->role->module_permissions,
        ] : null,
        'has_land_owners_access' => $user->hasModuleAccess('land-owners'),
        'has_land_owners_read' => $user->hasPermission('land-owners', 'read'),
    ]);
});

// Catch-all route for React Router - this should handle all dashboard routes
Route::get('/{any}', function () {
    return view('react');
})->where('any', '^(?!api).*$'); // Exclude API routes