<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_addresses', function (Blueprint $table) {
            // Remove old complex address fields
            $table->dropColumn([
                'plot_no',
                'road', 
                'area',
                'upazila',
                'thana',
                'city',
                'district',
                'country',
                'zip_code'
            ]);
            
            // Add new foreign key fields for country, state, city
            $table->foreignId('country_id')->nullable()->constrained('countries')->onDelete('set null');
            $table->foreignId('state_id')->nullable()->constrained('states')->onDelete('set null');
            $table->foreignId('city_id')->nullable()->constrained('cities')->onDelete('set null');
            
            // Add optional specific address details
            $table->string('specific_address')->nullable()->comment('Specific address like plot number, road, etc.');
            
            // Add indexes for better performance
            $table->index(['country_id', 'state_id', 'city_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_addresses', function (Blueprint $table) {
            // Drop new columns
            $table->dropForeign(['country_id']);
            $table->dropForeign(['state_id']);
            $table->dropForeign(['city_id']);
            $table->dropColumn(['country_id', 'state_id', 'city_id', 'specific_address']);
            
            // Restore old columns
            $table->string('plot_no')->nullable();
            $table->string('road')->nullable();
            $table->string('area')->nullable();
            $table->string('upazila')->nullable();
            $table->string('thana')->nullable();
            $table->string('city')->nullable();
            $table->string('district')->nullable();
            $table->string('country')->default('Bangladesh');
            $table->string('zip_code')->nullable();
        });
    }
};
