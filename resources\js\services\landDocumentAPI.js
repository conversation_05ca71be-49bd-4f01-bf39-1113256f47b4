import axios from 'axios';

// Get base URL from environment or default to localhost
const getBaseUrl = () => {
  // Check if we're in development or production
  const hostname = window.location.hostname;
  const port = window.location.port;
  const protocol = window.location.protocol;
  
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return `${protocol}//${hostname}:${port || '80'}/api`;
  }
  
  // For production or other environments
  return `${protocol}//${hostname}/api`;
};

const API_BASE_URL = getBaseUrl();

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token'); // Changed from 'token' to 'auth_token'
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token'); // Changed from 'token' to 'auth_token'
      window.location.reload(); // Changed from redirect to reload to trigger auth context
    }
    return Promise.reject(error);
  }
);

// Land Documents API functions
export const landDocumentAPI = {
  // Get all documents for a specific land acquisition
  getByLandAcquisition: async (landAcquisitionId) => {
    try {
      const response = await apiClient.get('/land-documents', {
        params: { land_acquisition_id: landAcquisitionId }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching land documents:', error);
      throw error;
    }
  },

  // Get all documents
  getAll: async (params = {}) => {
    try {
      const response = await apiClient.get('/land-documents', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching land documents:', error);
      throw error;
    }
  },

  // Get single document
  getById: async (id) => {
    try {
      const response = await apiClient.get(`/land-documents/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching land document:', error);
      throw error;
    }
  },

  // Create new document
  create: async (documentData) => {
    try {
      const formData = new FormData();
      
      // Append all form fields
      Object.keys(documentData).forEach(key => {
        if (documentData[key] !== null && documentData[key] !== undefined) {
          formData.append(key, documentData[key]);
        }
      });

      const response = await apiClient.post('/land-documents', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error creating land document:', error);
      throw error;
    }
  },

  // Update document
  update: async (id, documentData) => {
    try {
      const formData = new FormData();
      
      // Add method override for Laravel
      formData.append('_method', 'PUT');
      
      // Append all form fields
      Object.keys(documentData).forEach(key => {
        if (documentData[key] !== null && documentData[key] !== undefined) {
          formData.append(key, documentData[key]);
        }
      });

      const response = await apiClient.post(`/land-documents/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error updating land document:', error);
      throw error;
    }
  },

  // Delete document
  delete: async (id) => {
    try {
      const response = await apiClient.delete(`/land-documents/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting land document:', error);
      throw error;
    }
  },

  // Download document
  download: async (id) => {
    try {
      const response = await apiClient.get(`/land-documents/${id}/download`, {
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      console.error('Error downloading land document:', error);
      throw error;
    }
  },

  // Bulk upload documents
  bulkUpload: async (landAcquisitionId, documents) => {
    try {
      const results = [];
      
      for (const document of documents) {
        if (document.isNew && document.document_file) {
          const documentData = {
            land_acquisition_id: landAcquisitionId,
            document_name: document.document_name,
            document_file: document.document_file,
            description: document.description
          };
          
          const result = await landDocumentAPI.create(documentData);
          results.push(result);
        }
      }
      
      return {
        success: true,
        data: results,
        message: `${results.length} documents uploaded successfully`
      };
    } catch (error) {
      console.error('Error bulk uploading documents:', error);
      throw error;
    }
  }
};

export default landDocumentAPI;
