import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import * as landOwnerAPI from '../../services/landOwnerAPI';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/hooks/useTranslation';
import SimpleImageUpload from '@/components/ui/SimpleImageUpload';
import ProfileImageUpload from '@/components/ui/ProfileImageUpload';
import DocumentUpload from '@/components/ui/DocumentUpload';
import DocumentViewer from '@/components/ui/DocumentViewer';
import ImageModal from '@/components/ui/ImageModal';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  Phone,
  Mail,
  MapPin,
  ChevronLeft,
  ChevronRight,
  FileText,
  Calendar,
  User,
  Activity,
  UserCheck,
  UserX,
  Shield,
  ShieldOff,
} from 'lucide-react';

const LandOwnersPage = () => {
  const { t } = useTranslation();
  
  // State management
  const [landOwners, setLandOwners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingOwner, setEditingOwner] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [statistics, setStatistics] = useState({
    total_owners: 0,
    active_owners: 0,
    inactive_owners: 0,
    recent_registrations: 0
  });

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    father_name: '',
    mother_name: '',
    address: '',
    phone: '',
    nid_number: '',
    email: '',
    photo: '',
    document_type: 'nid',
    nid_front: '',
    nid_back: '',
    passport_photo: ''
  });

  const [editFormData, setEditFormData] = useState({
    first_name: '',
    last_name: '',
    father_name: '',
    mother_name: '',
    address: '',
    phone: '',
    nid_number: '',
    email: '',
    photo: '',
    document_type: 'nid',
    nid_front: '',
    nid_back: '',
    passport_photo: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Image modal state
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState({ url: '', ownerName: '', title: '' });

  // Log modal state
  const [showLogModal, setShowLogModal] = useState(false);
  const [auditLogs, setAuditLogs] = useState([]);
  const [loadingLogs, setLoadingLogs] = useState(false);

  // Sweet Alert helper
  const showAlert = {
    success: (title, text) => {
      Swal.fire({
        icon: 'success',
        title: title,
        text: text,
        confirmButtonColor: '#10b981',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    error: (title, text) => {
      Swal.fire({
        icon: 'error',
        title: title,
        text: text,
        confirmButtonColor: '#ef4444',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    warning: (title, text) => {
      Swal.fire({
        icon: 'warning',
        title: title,
        text: text,
        confirmButtonColor: '#f59e0b',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    loading: (title, text) => {
      Swal.fire({
        title: title,
        text: text,
        allowOutsideClick: false,
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        },
        didOpen: () => {
          Swal.showLoading();
        }
      });
    }
  };

  // API functions
  const fetchLandOwners = async (search = '', page = 1, limit = 10) => {
    try {
      setLoading(true);
      const params = {
        page: page.toString(),
        per_page: limit.toString(),
        ...(search && { search })
      };

      const result = await landOwnerAPI.getAll(params);

      if (result.success) {
        setLandOwners(result.data.data || []);
        setCurrentPage(result.data.current_page || 1);
        setTotalPages(result.data.last_page || 1);
        setTotalRecords(result.data.total || 0);
        
        // Calculate statistics
        const owners = result.data.data || [];
        const total = result.data.total || 0;
        const active = owners.filter(owner => owner.status === 'active').length;
        const inactive = owners.filter(owner => owner.status === 'inactive').length;
        const recent = owners.filter(owner => {
          const created = new Date(owner.created_at);
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          return created > thirtyDaysAgo;
        }).length;
        
        setStatistics({
          total_owners: total,
          active_owners: active,
          inactive_owners: inactive,
          recent_registrations: recent
        });
      }
    } catch (error) {
      console.error('Error fetching land owners:', error);
      showAlert.error('Error!', 'Failed to fetch land owners. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch audit logs
  const fetchAuditLogs = async () => {
    try {
      setLoadingLogs(true);
      const result = await landOwnerAPI.getAllAudits();
      
      if (result.success) {
        setAuditLogs(result.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      showAlert.error('Error!', 'Failed to fetch audit logs. Please try again.');
    } finally {
      setLoadingLogs(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditInputChange = (field, value) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle opening log modal
  const handleOpenLogModal = () => {
    setShowLogModal(true);
    fetchAuditLogs();
  };

  // Helper function to get full image URL
  const getImageUrl = (photoPath) => {
    // Check if photoPath is null, undefined, or not a string
    if (!photoPath || typeof photoPath !== 'string') return null;
    
    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }
    
    // If it starts with /landowners, use the base URL directly
    if (photoPath.startsWith('/landowners/')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it starts with /storage, use the base URL
    if (photoPath.startsWith('/storage')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it's just a filename or relative path, assume it's a photo in landowners/photo
    return `${window.location.protocol}//${window.location.host}/landowners/photo/${photoPath}`;
  };

  // Handle opening image modal
  const handleImageClick = (imageUrl, ownerName) => {
    const fullImageUrl = getImageUrl(imageUrl);
    if (fullImageUrl) {
      setSelectedImage({
        url: fullImageUrl,
        ownerName: ownerName,
        title: `${ownerName}'s Photo`
      });
      setShowImageModal(true);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form data - Image is NOT mandatory
    if (!formData.first_name || !formData.last_name || !formData.father_name || !formData.address) {
      showAlert.warning('Validation Error!', 'Please fill in all required fields: First Name, Last Name, Father\'s Name, and Address.');
      return;
    }

    // Optional: Validate email format if provided
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      showAlert.warning('Validation Error!', 'Please enter a valid email address.');
      return;
    }

    setIsSubmitting(true);

    // Show loading alert
    showAlert.loading(
      'Creating Land Owner...',
      'Please wait while we create the land owner record.'
    );

    try {
      console.log('📝 Form data before submission:', {
        ...formData,
        photo: formData.photo instanceof File ? `File(${formData.photo.name}, ${formData.photo.size} bytes)` : formData.photo,
        nid_front: formData.nid_front instanceof File ? `File(${formData.nid_front.name}, ${formData.nid_front.size} bytes)` : formData.nid_front,
        nid_back: formData.nid_back instanceof File ? `File(${formData.nid_back.name}, ${formData.nid_back.size} bytes)` : formData.nid_back,
        passport_photo: formData.passport_photo instanceof File ? `File(${formData.passport_photo.name}, ${formData.passport_photo.size} bytes)` : formData.passport_photo,
      });

      const result = await landOwnerAPI.create(formData);
      console.log('✅ Create API Response:', result);

      if (result.success) {
        // Reset form
        setFormData({
          first_name: '',
          last_name: '',
          father_name: '',
          mother_name: '',
          address: '',
          phone: '',
          nid_number: '',
          email: '',
          photo: '',
          document_type: 'nid',
          nid_front: '',
          nid_back: '',
          passport_photo: ''
        });

        setShowAddForm(false);

        // Refresh data
        fetchLandOwners(searchTerm, currentPage, perPage);

        // Success alert
        showAlert.success(
          'Success!',
          'Land owner created successfully!'
        );
      } else {
        // Handle validation errors or other API errors
        let errorMessage = result.message || 'Unknown error occurred';
        
        if (result.errors) {
          // If there are validation errors, show them
          const errorList = Object.values(result.errors).flat().join('\n');
          errorMessage = `Validation errors:\n${errorList}`;
        }
        
        showAlert.error(
          'Error!',
          errorMessage
        );
      }
    } catch (error) {
      console.error('❌ Error creating land owner:', error);
      
      let errorMessage = 'Error creating land owner. Please try again.';
      
      if (error.response && error.response.data) {
        if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.errors) {
          const errorList = Object.values(error.response.data.errors).flat().join('\n');
          errorMessage = `Validation errors:\n${errorList}`;
        }
      }
      
      showAlert.error(
        'Error!',
        errorMessage
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchLandOwners(searchTerm, 1, perPage);
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      fetchLandOwners(searchTerm, newPage, perPage);
    }
  };

  const handleEditOwner = (owner) => {
    setEditingOwner(owner);
    setEditFormData({
      first_name: owner.first_name || '',
      last_name: owner.last_name || '',
      father_name: owner.father_name || '',
      mother_name: owner.mother_name || '',
      address: owner.address || '',
      phone: owner.phone || '',
      nid_number: owner.nid_number || '',
      email: owner.email || '',
      photo: owner.photo || '',
      document_type: owner.document_type || 'nid',
      nid_front: owner.nid_front || '',
      nid_back: owner.nid_back || '',
      passport_photo: owner.passport_photo || ''
    });
    setShowEditForm(true);
  };

  const handleDeleteOwner = async (owner) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to delete ${owner.first_name} ${owner.last_name}? This action cannot be undone!`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      customClass: {
        popup: 'swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high'
      }
    });

    if (result.isConfirmed) {
      try {
        const deleteResult = await landOwnerAPI.delete_(owner.id);
        console.log('Delete result:', deleteResult);

        if (deleteResult.success) {
          // Refresh data
          fetchLandOwners(searchTerm, currentPage, perPage);

          showAlert.success(
            'Deleted!',
            'Land owner has been deleted successfully.'
          );
        } else {
          showAlert.error(
            'Error!',
            deleteResult.message || 'Failed to delete land owner.'
          );
        }
      } catch (error) {
        console.error('Error deleting land owner:', error);
        showAlert.error(
          'Error!',
          'Error deleting land owner. Please try again.'
        );
      }
    }
  };

  // Handle activate land owner
  const handleActivateOwner = async (owner) => {
    const result = await Swal.fire({
      title: 'Activate Land Owner',
      text: `Do you want to activate ${owner.first_name} ${owner.last_name}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, activate!',
      customClass: {
        popup: 'swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high'
      }
    });

    if (result.isConfirmed) {
      try {
        const activateResult = await landOwnerAPI.activate(owner.id);
        console.log('Activate result:', activateResult);

        if (activateResult.success) {
          // Refresh data
          fetchLandOwners(searchTerm, currentPage, perPage);

          showAlert.success(
            'Activated!',
            'Land owner has been activated successfully.'
          );
        } else {
          showAlert.error(
            'Error!',
            activateResult.message || 'Failed to activate land owner.'
          );
        }
      } catch (error) {
        console.error('Error activating land owner:', error);
        showAlert.error(
          'Error!',
          'Error activating land owner. Please try again.'
        );
      }
    }
  };

  // Handle deactivate land owner
  const handleDeactivateOwner = async (owner) => {
    const result = await Swal.fire({
      title: 'Deactivate Land Owner',
      text: `Do you want to deactivate ${owner.first_name} ${owner.last_name}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#f59e0b',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, deactivate!',
      customClass: {
        popup: 'swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high'
      }
    });

    if (result.isConfirmed) {
      try {
        const deactivateResult = await landOwnerAPI.deactivate(owner.id);
        console.log('Deactivate result:', deactivateResult);

        if (deactivateResult.success) {
          // Refresh data
          fetchLandOwners(searchTerm, currentPage, perPage);

          showAlert.success(
            'Deactivated!',
            'Land owner has been deactivated successfully.'
          );
        } else {
          showAlert.error(
            'Error!',
            deactivateResult.message || 'Failed to deactivate land owner.'
          );
        }
      } catch (error) {
        console.error('Error deactivating land owner:', error);
        showAlert.error(
          'Error!',
          'Error deactivating land owner. Please try again.'
        );
      }
    }
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();

    // Validate form data - Image is NOT mandatory
    if (!editFormData.first_name || !editFormData.last_name || !editFormData.father_name || !editFormData.address) {
      showAlert.warning('Validation Error!', 'Please fill in all required fields: First Name, Last Name, Father\'s Name, and Address.');
      return;
    }

    // Optional: Validate email format if provided
    if (editFormData.email && !/\S+@\S+\.\S+/.test(editFormData.email)) {
      showAlert.warning('Validation Error!', 'Please enter a valid email address.');
      return;
    }

    setIsUpdating(true);

    // Show loading alert
    showAlert.loading(
      'Updating Land Owner...',
      'Please wait while we update the land owner record.'
    );

    try {
      // Debug logging
      console.log('📝 Edit form data before submission:', {
        ...editFormData,
        photo: editFormData.photo instanceof File ?
          `File(${editFormData.photo.name}, ${editFormData.photo.size} bytes)` :
          editFormData.photo,
        nid_front: editFormData.nid_front instanceof File ?
          `File(${editFormData.nid_front.name}, ${editFormData.nid_front.size} bytes)` :
          editFormData.nid_front,
        nid_back: editFormData.nid_back instanceof File ?
          `File(${editFormData.nid_back.name}, ${editFormData.nid_back.size} bytes)` :
          editFormData.nid_back,
        passport_photo: editFormData.passport_photo instanceof File ?
          `File(${editFormData.passport_photo.name}, ${editFormData.passport_photo.size} bytes)` :
          editFormData.passport_photo,
      });

      const result = await landOwnerAPI.update(editingOwner.id, editFormData);
      console.log('✅ Update API Response:', result);

      if (result.success) {
        // Reset form
        setEditFormData({
          first_name: '',
          last_name: '',
          father_name: '',
          mother_name: '',
          address: '',
          phone: '',
          nid_number: '',
          email: '',
          photo: '',
          document_type: 'nid',
          nid_front: '',
          nid_back: '',
          passport_photo: ''
        });

        setShowEditForm(false);
        setEditingOwner(null);

        // Refresh data
        fetchLandOwners(searchTerm, currentPage, perPage);

        // Success alert
        showAlert.success(
          'Success!',
          'Land owner updated successfully!'
        );
      } else {
        // Handle validation errors or other API errors
        let errorMessage = result.message || 'Unknown error occurred';
        
        if (result.errors) {
          // If there are validation errors, show them
          const errorList = Object.values(result.errors).flat().join('\n');
          errorMessage = `Validation errors:\n${errorList}`;
        }
        
        showAlert.error(
          'Error!',
          errorMessage
        );
      }
    } catch (error) {
      console.error('❌ Error updating land owner:', error);
      
      let errorMessage = 'Error updating land owner. Please try again.';
      
      if (error.response && error.response.data) {
        if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.errors) {
          const errorList = Object.values(error.response.data.errors).flat().join('\n');
          errorMessage = `Validation errors:\n${errorList}`;
        }
      }
      
      showAlert.error(
        'Error!',
        errorMessage
      );
    } finally {
      setIsUpdating(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchLandOwners('', 1, perPage);
  }, [perPage]);

  const StatCard = ({ title, value, icon: Icon, color, description }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${color} mr-4`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {description && (
            <p className="text-xs text-gray-400">{description}</p>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('landOwners.header.title')}</h1>
          <p className="text-muted-foreground">
            {t('landOwners.header.description')}
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          {t('landOwners.addOwner')}
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <StatCard
          title={t('landOwners')}
          value={statistics.total_owners}
          icon={Users}
          color="bg-blue-500"
          description="Total registered land owners"
        />
        <StatCard
          title={t('landOwners.stats.activeLandOwners')}
          value={statistics.active_owners}
          icon={User}
          color="bg-green-500"
          description="Currently active owners"
        />
        <StatCard
          title={t('landOwners.stats.inactiveOwners')}
          value={statistics.inactive_owners}
          icon={UserX}
          color="bg-red-500"
          description="Inactive land owners"
        />
        <StatCard
          title={t('landOwners.stats.recentRegistrations')}
          value={statistics.recent_registrations}
          icon={Calendar}
          color="bg-purple-500"
          description="New owners in last 30 days"
        />
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            {t('landOwners.searchTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1 no-focus-outline">
              <style jsx>{`
                .no-focus-outline input {
                  transition: all 0.2s ease-in-out;
                  transform: scale(1);
                }
                .no-focus-outline input:focus {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:focus-visible {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:hover {
                  border-color: #9ca3af !important;
                  transform: scale(1.01) !important;
                }
              `}</style>
              <Input
                placeholder={t('landOwners.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button type="submit">
              <Search className="mr-2 h-4 w-4" />
              {t('common.buttons.search')}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Land Owners Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t('landOwners.title')} ({totalRecords})
              </CardTitle>
              <CardDescription>
                {t('landOwners.description')}
              </CardDescription>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleOpenLogModal}
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              {t('common.auditLog')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : landOwners.length === 0 ? (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No land owners found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by creating a new land owner.'}
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('landOwners.table.serialNumber')}</TableHead>
                    <TableHead>{t('landOwners.table.photo')}</TableHead>
                    <TableHead>{t('landOwners.table.firstName')}</TableHead>
                    <TableHead>{t('landOwners.table.lastName')}</TableHead>
                    <TableHead>{t('landOwners.table.fatherName')}</TableHead>
                    <TableHead>{t('landOwners.table.nidNumber')}</TableHead>
                    <TableHead>{t('landOwners.table.contact')}</TableHead>
                    <TableHead>{t('landOwners.table.address')}</TableHead>
                    <TableHead>{t('landOwners.table.status')}</TableHead>
                    <TableHead>{t('landOwners.table.documents')}</TableHead>
                    <TableHead className="text-right">{t('landOwners.table.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {landOwners.map((owner, index) => (
                    <TableRow key={owner.id}>
                     <TableCell className="text-center text-muted-foreground">
                        {((currentPage - 1) * perPage) + index + 1}
                      </TableCell>
                      <TableCell>
                        {owner.photo ? (
                          <img
                            src={getImageUrl(owner.photo)}
                            alt={`${owner.first_name} ${owner.last_name}`}
                            style={{ width: '60px', height: '60px', objectFit: 'cover', borderRadius: '8px', cursor: 'pointer' }}
                            onClick={() => handleImageClick(owner.photo, `${owner.first_name} ${owner.last_name}`)}
                          />
                        ) : (
                          <div 
                            style={{ 
                              width: '60px', 
                              height: '60px', 
                              backgroundColor: '#f3f4f6', 
                              borderRadius: '8px', 
                              display: 'flex', 
                              alignItems: 'center', 
                              justifyContent: 'center',
                              fontSize: '12px',
                              color: '#6b7280'
                            }}
                          >
                            {t('landOwners.table.noImage')}
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">{owner.first_name}</TableCell>
                      <TableCell className="font-medium">{owner.last_name}</TableCell>
                      <TableCell>{owner.father_name}</TableCell>
                      <TableCell>{owner.nid_number || '-'}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {owner.phone && (
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3" />
                              {owner.phone}
                            </div>
                          )}
                          {owner.email && (
                            <div className="flex items-center gap-1 text-sm">
                              <Mail className="h-3 w-3" />
                              {owner.email}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm">
                          <MapPin className="h-3 w-3" />
                          <span className="truncate max-w-[200px]">{owner.address}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={owner.status === 'active' ? 'default' : 'secondary'}
                          className={owner.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                        >
                          {owner.status === 'active' ? t('landOwners.table.active') : t('landOwners.table.inactive')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DocumentViewer 
                          owner={owner}
                          onViewImage={handleImageClick}
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditOwner(owner)}>
                              <Edit className="mr-2 h-4 w-4" />
                              {t('landOwners.table.edit')}
                            </DropdownMenuItem>
                            {owner.status === 'active' ? (
                              <DropdownMenuItem
                                onClick={() => handleDeactivateOwner(owner)}
                                className="text-orange-600"
                              >
                                <UserX className="mr-2 h-4 w-4" />
                                {t('landOwners.table.deactivate')}
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem
                                onClick={() => handleActivateOwner(owner)}
                                className="text-green-600"
                              >
                                <UserCheck className="mr-2 h-4 w-4" />
                                {t('landOwners.table.activate')}
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={() => handleDeleteOwner(owner)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t('landOwners.table.delete')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalRecords)} of {totalRecords} entries
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                          >
                            {page}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage >= totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add New Owner Modal */}
      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>{t('landOwners.forms.addTitle')}</DialogTitle>
            <DialogDescription>
              {t('landOwners.forms.addDescription')}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto pr-2">
            <form onSubmit={handleSubmit} className="space-y-6 ml-2">
              <style jsx>{`
                .no-focus-outline input {
                  transition: all 0.2s ease-in-out;
                  transform: scale(1);
                }
                .no-focus-outline input:focus {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:focus-visible {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:hover {
                  border-color: #9ca3af !important;
                  transform: scale(1.01) !important;
                }
              `}</style>
              
              {/* Profile Photo Section */}
              <div className="border-b pb-6">
                <ProfileImageUpload
                  value={formData.photo}
                  onChange={(file) => handleInputChange('photo', file)}
                  name={`${formData.first_name} ${formData.last_name}`.trim() || 'Owner'}
                  disabled={isSubmitting}
                />
              </div>

              {/* Basic Information */}
              <div className="space-y-4">
                {/* <h3 className="text-lg font-medium text-gray-900">Basic Information</h3> */}
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2 no-focus-outline">
                    <Label htmlFor="first_name">{t('landOwners.forms.firstNameLabel')} {t('landOwners.forms.required')}</Label>
                    <Input
                      id="first_name"
                      value={formData.first_name}
                      onChange={(e) => handleInputChange('first_name', e.target.value)}
                      placeholder={t('landOwners.forms.firstNamePlaceholder')}
                      required
                    />
                  </div>

                  <div className="space-y-2 no-focus-outline">
                    <Label htmlFor="last_name">{t('landOwners.forms.lastNameLabel')} {t('landOwners.forms.required')}</Label>
                    <Input
                      id="last_name"
                      value={formData.last_name}
                      onChange={(e) => handleInputChange('last_name', e.target.value)}
                      placeholder={t('landOwners.forms.lastNamePlaceholder')}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="father_name">{t('landOwners.forms.fatherNameLabel')} {t('landOwners.forms.required')}</Label>
                  <Input
                    id="father_name"
                    value={formData.father_name}
                    onChange={(e) => handleInputChange('father_name', e.target.value)}
                    placeholder={t('landOwners.forms.fatherNamePlaceholder')}
                    required
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="mother_name">{t('landOwners.forms.motherNameLabel')}</Label>
                  <Input
                    id="mother_name"
                    value={formData.mother_name}
                    onChange={(e) => handleInputChange('mother_name', e.target.value)}
                    placeholder={t('landOwners.forms.motherNamePlaceholder')}
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="address">{t('landOwners.forms.addressLabel')} {t('landOwners.forms.required')}</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    placeholder={t('landOwners.forms.addressPlaceholder')}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2 no-focus-outline">
                    <Label htmlFor="phone">{t('landOwners.forms.phoneLabel')}</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder={t('landOwners.forms.phonePlaceholder')}
                    />
                  </div>

                  <div className="space-y-2 no-focus-outline">
                    <Label htmlFor="nid_number">{t('landOwners.forms.nidLabel')}</Label>
                    <Input
                      id="nid_number"
                      value={formData.nid_number}
                      onChange={(e) => handleInputChange('nid_number', e.target.value)}
                      placeholder={t('landOwners.forms.nidPlaceholder')}
                    />
                  </div>
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="email">{t('landOwners.forms.emailLabel')}</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder={t('landOwners.forms.emailPlaceholder')}
                  />
                </div>
              </div>

              {/* Document Upload Section */}
              <div className="border-t pt-6">
                <DocumentUpload
                  documentType={formData.document_type}
                  onDocumentTypeChange={(type) => handleInputChange('document_type', type)}
                  nidFront={formData.nid_front}
                  onNidFrontChange={(file) => handleInputChange('nid_front', file)}
                  nidBack={formData.nid_back}
                  onNidBackChange={(file) => handleInputChange('nid_back', file)}
                  passportPhoto={formData.passport_photo}
                  onPassportPhotoChange={(file) => handleInputChange('passport_photo', file)}
                  disabled={isSubmitting}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                  disabled={isSubmitting}
                >
                  {t('landOwners.forms.cancel')}
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? t('landOwners.forms.creating') : t('landOwners.forms.create')}
                </Button>
              </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Owner Modal */}
      <Dialog open={showEditForm} onOpenChange={setShowEditForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle>{t('landOwners.forms.editTitle')}</DialogTitle>
            <DialogDescription>
              {t('landOwners.forms.editDescription')}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto pr-2">
            <form onSubmit={handleUpdateSubmit} className="space-y-6 ml-2">
              <style jsx>{`
                .no-focus-outline input {
                  transition: all 0.2s ease-in-out;
                  transform: scale(1);
                }
                .no-focus-outline input:focus {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:focus-visible {
                  outline: none !important;
                  border-color: #d1d5db !important;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                  transform: scale(1.02) !important;
                }
                .no-focus-outline input:hover {
                  border-color: #9ca3af !important;
                  transform: scale(1.01) !important;
                }
              `}</style>
              
              {/* Profile Photo Section */}
              <div className="border-b pb-6">
                <ProfileImageUpload
                  value={editFormData.photo}
                  onChange={(file) => handleEditInputChange('photo', file)}
                  name={`${editFormData.first_name} ${editFormData.last_name}`.trim() || 'Owner'}
                  disabled={isUpdating}
                />
              </div>

              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">{t('landOwners.forms.basicInfo')}</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2 no-focus-outline">
                    <Label htmlFor="edit_first_name">{t('landOwners.forms.firstNameLabel')} {t('landOwners.forms.required')}</Label>
                    <Input
                      id="edit_first_name"
                      value={editFormData.first_name}
                      onChange={(e) => handleEditInputChange('first_name', e.target.value)}
                      placeholder={t('landOwners.forms.firstNamePlaceholder')}
                      required
                    />
                  </div>

                  <div className="space-y-2 no-focus-outline">
                    <Label htmlFor="edit_last_name">{t('landOwners.forms.lastNameLabel')} {t('landOwners.forms.required')}</Label>
                    <Input
                      id="edit_last_name"
                      value={editFormData.last_name}
                      onChange={(e) => handleEditInputChange('last_name', e.target.value)}
                      placeholder={t('landOwners.forms.lastNamePlaceholder')}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="edit_father_name">{t('landOwners.forms.fatherNameLabel')} {t('landOwners.forms.required')}</Label>
                  <Input
                    id="edit_father_name"
                    value={editFormData.father_name}
                    onChange={(e) => handleEditInputChange('father_name', e.target.value)}
                    placeholder={t('landOwners.forms.fatherNamePlaceholder')}
                    required
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="edit_mother_name">{t('landOwners.forms.motherNameLabel')}</Label>
                  <Input
                    id="edit_mother_name"
                    value={editFormData.mother_name}
                    onChange={(e) => handleEditInputChange('mother_name', e.target.value)}
                    placeholder={t('landOwners.forms.motherNamePlaceholder')}
                  />
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="edit_address">{t('landOwners.forms.addressLabel')} {t('landOwners.forms.required')}</Label>
                  <Input
                    id="edit_address"
                    value={editFormData.address}
                    onChange={(e) => handleEditInputChange('address', e.target.value)}
                    placeholder={t('landOwners.forms.addressPlaceholder')}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2 no-focus-outline">
                    <Label htmlFor="edit_phone">{t('landOwners.forms.phoneLabel')}</Label>
                    <Input
                      id="edit_phone"
                      value={editFormData.phone}
                      onChange={(e) => handleEditInputChange('phone', e.target.value)}
                      placeholder={t('landOwners.forms.phonePlaceholder')}
                    />
                  </div>

                  <div className="space-y-2 no-focus-outline">
                    <Label htmlFor="edit_nid_number">{t('landOwners.forms.nidLabel')} {t('landOwners.forms.required')}</Label>
                    <Input
                      id="edit_nid_number"
                      value={editFormData.nid_number}
                      onChange={(e) => handleEditInputChange('nid_number', e.target.value)}
                      placeholder={t('landOwners.forms.nidPlaceholder')}
                    />
                  </div>
                </div>

                <div className="space-y-2 no-focus-outline">
                  <Label htmlFor="edit_email">{t('landOwners.forms.emailLabel')}</Label>
                  <Input
                    id="edit_email"
                    type="email"
                    value={editFormData.email}
                    onChange={(e) => handleEditInputChange('email', e.target.value)}
                    placeholder={t('landOwners.forms.emailPlaceholder')}
                  />
                </div>
              </div>

              {/* Document Upload Section */}
              <div className="border-t pt-6">
                <DocumentUpload
                  documentType={editFormData.document_type}
                  onDocumentTypeChange={(type) => handleEditInputChange('document_type', type)}
                  nidFront={editFormData.nid_front}
                  onNidFrontChange={(file) => handleEditInputChange('nid_front', file)}
                  nidBack={editFormData.nid_back}
                  onNidBackChange={(file) => handleEditInputChange('nid_back', file)}
                  passportPhoto={editFormData.passport_photo}
                  onPassportPhotoChange={(file) => handleEditInputChange('passport_photo', file)}
                  disabled={isUpdating}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowEditForm(false)}
                  disabled={isUpdating}
                >
                  {t('landOwners.forms.cancel')}
                </Button>
                <Button type="submit" disabled={isUpdating}>
                  {isUpdating ? t('landOwners.forms.updating') : t('landOwners.forms.update')}
                </Button>
              </DialogFooter>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Image Modal */}
      <ImageModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        imageUrl={selectedImage.url}
        title={selectedImage.title}
        ownerName={selectedImage.ownerName}
      />

      {/* Audit Log Modal */}
      <Dialog open={showLogModal} onOpenChange={setShowLogModal}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              {t('landOwners.auditLog.title')}
            </DialogTitle>
            <DialogDescription>
              {t('landOwners.auditLog.description')}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 overflow-auto">
            {loadingLogs ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              </div>
            ) : auditLogs.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">{t('landOwners.auditLog.noLogs')}</h3>
                <p className="mt-1 text-sm text-gray-500">{t('landOwners.auditLog.noActivity')}</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('landOwners.auditLog.dateTime')}</TableHead>
                    <TableHead>{t('landOwners.auditLog.action')}</TableHead>
                    <TableHead>{t('landOwners.auditLog.landOwner')}</TableHead>
                    <TableHead>{t('landOwners.auditLog.user')}</TableHead>
                    <TableHead>{t('landOwners.auditLog.details')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium">
                              {new Date(log.created_at).toLocaleDateString()}
                            </div>
                            <div className="text-sm text-gray-500">
                              {new Date(log.created_at).toLocaleTimeString()}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            log.action === 'created' ? 'default' :
                            log.action === 'updated' ? 'secondary' :
                            log.action === 'deleted' ? 'destructive' : 'outline'
                          }
                          className="capitalize"
                        >
                          {log.action}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {log.land_owner ? 
                            `${log.land_owner.first_name} ${log.land_owner.last_name}` : 
                            'N/A'
                          }
                        </div>
                        {log.land_owner_id && (
                          <div className="text-sm text-gray-500">ID: {log.land_owner_id}</div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium">{log.user ? log.user.name : 'Unknown'}</div>
                            <div className="text-sm text-gray-500">{log.user ? log.user.email : 'N/A'}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs">
                          <div className="text-sm text-gray-900">{log.description || 'No details'}</div>
                          {log.detailed_changes && log.detailed_changes.length > 0 && (
                            <div className="mt-2 space-y-1">
                              <div className="text-xs font-medium text-gray-700">{t('landOwners.auditLog.changes')}</div>
                              {log.detailed_changes.map((change, idx) => (
                                <div key={idx} className="text-xs bg-gray-50 p-2 rounded border">
                                  <div className="font-medium text-gray-800">{change.field}:</div>
                                  <div className="flex items-center gap-2 mt-1">
                                    <span className="text-red-600 line-through">{change.old_value}</span>
                                    <span className="text-gray-400">→</span>
                                    <span className="text-green-600">{change.new_value}</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLogModal(false)}>
              {t('landOwners.auditLog.close')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LandOwnersPage;
