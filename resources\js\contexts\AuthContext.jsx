import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../services/authAPI';

const AuthContext = createContext({
  user: null,
  role: null,
  permissions: {},
  accessibleModules: [],
  hasModuleAccess: () => false,
  hasPermission: () => false,
  login: () => {},
  logout: () => {},
  loading: true,
  isAuthenticated: false
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [role, setRole] = useState(null);
  const [permissions, setPermissions] = useState({});
  const [accessibleModules, setAccessibleModules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Load user data on mount if token exists
  useEffect(() => {
    const loadUserData = async () => {
      if (authAPI.isAuthenticated()) {
        try {
          const response = await authAPI.getPermissions();
          if (response.success) {
            setUser(response.data.user);
            setRole(response.data.role);
            setPermissions(response.data.permissions);
            setAccessibleModules(response.data.accessible_modules);
            setIsAuthenticated(true);
          } else {
            // Token might be invalid, clear it
            authAPI.removeToken();
            setIsAuthenticated(false);
          }
        } catch (error) {
          console.error('Failed to load user data:', error);
          authAPI.removeToken();
          setIsAuthenticated(false);
        }
      } else {
        setIsAuthenticated(false);
      }
      setLoading(false);
    };

    loadUserData();
  }, []);

  const hasModuleAccess = (moduleKey) => {
    return accessibleModules.includes(moduleKey);
  };

  const hasPermission = (moduleKey, permission) => {
    const modulePermissions = permissions[moduleKey] || [];
    return modulePermissions.includes(permission);
  };

  const login = async (credentials) => {
    try {
      const response = await authAPI.login(credentials);
      if (response.success) {
        setUser(response.data.user);
        setRole(response.data.user.role);
        setPermissions(response.data.user.role?.module_permissions || {});
        setAccessibleModules(response.data.user.role?.accessible_modules || []);
        setIsAuthenticated(true);
        return response;
      }
      return response;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setRole(null);
      setPermissions({});
      setAccessibleModules([]);
      setIsAuthenticated(false);
    }
  };

  const register = async (userData) => {
    try {
      const response = await authAPI.register(userData);
      if (response.success) {
        setUser(response.data.user);
        setRole(response.data.user.role);
        setPermissions(response.data.user.role?.module_permissions || {});
        setAccessibleModules(response.data.user.role?.accessible_modules || []);
        setIsAuthenticated(true);
        return response;
      }
      return response;
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await authAPI.updateProfile(profileData);
      if (response.success) {
        setUser(response.data);
      }
      return response;
    } catch (error) {
      console.error('Profile update failed:', error);
      throw error;
    }
  };

  const value = {
    user,
    role,
    permissions,
    accessibleModules,
    hasModuleAccess,
    hasPermission,
    login,
    logout,
    register,
    updateProfile,
    loading,
    isAuthenticated
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
