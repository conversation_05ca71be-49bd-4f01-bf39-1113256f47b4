<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LandAcquisitionController;
use App\Http\Controllers\LandOwnerController;
use App\Http\Controllers\LandDocumentController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ProductionDiagnosticsController;
use App\Http\Controllers\CountryController;
use App\Http\Controllers\StateController;
use App\Http\Controllers\CityController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\CurrencyController;






// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    
    // Protected authentication routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('logout-all', [AuthController::class, 'logoutAll']);
        Route::get('profile', [AuthController::class, 'profile']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
        Route::get('permissions', [AuthController::class, 'permissions']);
        Route::post('refresh-token', [AuthController::class, 'refreshToken']);
    });
});




// Protected API routes - require authentication
Route::middleware('auth:sanctum')->group(function () {
    


    // Land Acquisition routes - with permission checks
    Route::middleware('permission:land-acquisition,read')->group(function () {
        Route::get('land-acquisitions', [LandAcquisitionController::class, 'index']);
        Route::get('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'show']);
        Route::get('land-acquisitions-statistics', [LandAcquisitionController::class, 'statistics']);
    });
    
    Route::middleware('permission:land-acquisition,create')->group(function () {
        Route::post('land-acquisitions', [LandAcquisitionController::class, 'store']);
    });
    
    Route::middleware('permission:land-acquisition,update')->group(function () {
        Route::put('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']);
        Route::patch('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']);
        Route::post('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']); // For file uploads with _method=PUT
    });
    
    Route::middleware('permission:land-acquisition,delete')->group(function () {
        Route::delete('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'destroy']);
    });

    // Role Management routes - with permission checks
    Route::middleware('permission:role,read')->group(function () {
        Route::get('roles', [RoleController::class, 'index']);
        Route::get('roles/{role}', [RoleController::class, 'show']);
        Route::get('roles-statistics', [RoleController::class, 'getStatistics']);
        Route::get('roles-modules', [RoleController::class, 'getAvailableModules']);
    });
    
    Route::middleware('permission:role,create')->group(function () {
        Route::post('roles', [RoleController::class, 'store']);
    });
    
    Route::middleware('permission:role,update')->group(function () {
        Route::put('roles/{role}', [RoleController::class, 'update']);
        Route::patch('roles/{role}', [RoleController::class, 'update']);
        Route::patch('roles-bulk-status', [RoleController::class, 'bulkUpdateStatus']);
    });
    
    Route::middleware('permission:role,delete')->group(function () {
        Route::delete('roles/{role}', [RoleController::class, 'destroy']);
    });

    // Land Owner routes - with permission checks
    Route::middleware('permission:land-owners,read')->group(function () {
        Route::get('land-owners', [LandOwnerController::class, 'index']);
        Route::get('land-owners/{landOwner}', [LandOwnerController::class, 'show']);
        Route::get('land-owners-dropdown', [LandOwnerController::class, 'dropdown']);
        Route::get('land-owners/{id}/audit-history', [LandOwnerController::class, 'auditHistory']);
        Route::get('land-owners/{id}/detailed-audit-history', [LandOwnerController::class, 'detailedAuditHistory']);
        Route::get('land-owners-audits', [LandOwnerController::class, 'allAudits']);
        Route::get('land-owners-audit-stats', [LandOwnerController::class, 'auditStats']);
    });
    
    Route::middleware('permission:land-owners,create')->group(function () {
        Route::post('land-owners', [LandOwnerController::class, 'store']);
    });
    
    Route::middleware('permission:land-owners,update')->group(function () {
        Route::put('land-owners/{landOwner}', [LandOwnerController::class, 'update']);
        Route::patch('land-owners/{landOwner}', [LandOwnerController::class, 'update']);
        Route::post('land-owners/{landOwner}', [LandOwnerController::class, 'update']); // For file uploads with _method=PUT
        Route::patch('land-owners/{landOwner}/activate', [LandOwnerController::class, 'activate']);
        Route::patch('land-owners/{landOwner}/deactivate', [LandOwnerController::class, 'deactivate']);
    });
    
    Route::middleware('permission:land-owners,delete')->group(function () {
        Route::delete('land-owners/{landOwner}', [LandOwnerController::class, 'destroy']);
    });

    // Land Document routes - with permission checks
    Route::middleware('permission:land-acquisition,read')->group(function () {
        Route::get('land-documents', [LandDocumentController::class, 'index']);
        Route::get('land-documents/{landDocument}', [LandDocumentController::class, 'show']);
        Route::get('land-documents/{landDocument}/download', [LandDocumentController::class, 'download']);
    });
    
    Route::middleware('permission:land-acquisition,create')->group(function () {
        Route::post('land-documents', [LandDocumentController::class, 'store']);
    });
    
    Route::middleware('permission:land-acquisition,update')->group(function () {
        Route::put('land-documents/{landDocument}', [LandDocumentController::class, 'update']);
        Route::patch('land-documents/{landDocument}', [LandDocumentController::class, 'update']);
        Route::post('land-documents/{landDocument}', [LandDocumentController::class, 'update']); // For file uploads with _method=PUT
    });
    
    Route::middleware('permission:land-acquisition,delete')->group(function () {
        Route::delete('land-documents/{landDocument}', [LandDocumentController::class, 'destroy']);
    });

    // Country routes - with permission checks
    Route::middleware('permission:country,read')->group(function () {
        Route::get('countries', [CountryController::class, 'index']);
        Route::get('countries/{country}', [CountryController::class, 'show']);
        Route::get('countries-statistics', [CountryController::class, 'getStatistics']);
        Route::get('countries-continents', [CountryController::class, 'getContinents']);
    });
    
    Route::middleware('permission:country,create')->group(function () {
        Route::post('countries', [CountryController::class, 'store']);
    });
    
    Route::middleware('permission:country,update')->group(function () {
        Route::put('countries/{country}', [CountryController::class, 'update']);
        Route::patch('countries/{country}', [CountryController::class, 'update']);
        Route::patch('countries/{country}/toggle-status', [CountryController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:country,delete')->group(function () {
        Route::delete('countries/{country}', [CountryController::class, 'destroy']);
    });

    // State routes - with permission checks
    Route::middleware('permission:state,read')->group(function () {
        Route::get('states', [StateController::class, 'index']);
        Route::get('states/{state}', [StateController::class, 'show']);
        Route::get('states-statistics', [StateController::class, 'getStatistics']);
        Route::get('states/country/{countryId}', [StateController::class, 'byCountry']);
    });
    
    Route::middleware('permission:state,create')->group(function () {
        Route::post('states', [StateController::class, 'store']);
    });
    
    Route::middleware('permission:state,update')->group(function () {
        Route::put('states/{state}', [StateController::class, 'update']);
        Route::patch('states/{state}', [StateController::class, 'update']);
        Route::patch('states/{state}/toggle-status', [StateController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:state,delete')->group(function () {
        Route::delete('states/{state}', [StateController::class, 'destroy']);
    });

    // City routes - with permission checks
    Route::middleware('permission:city,read')->group(function () {
        Route::get('cities', [CityController::class, 'index']);
        Route::get('cities/{city}', [CityController::class, 'show']);
        Route::get('cities-statistics', [CityController::class, 'getStatistics']);
        Route::get('cities/state/{stateId}', [CityController::class, 'byState']);
        Route::get('cities/country/{countryId}', [CityController::class, 'byCountry']);
    });
    
    Route::middleware('permission:city,create')->group(function () {
        Route::post('cities', [CityController::class, 'store']);
    });
    
    Route::middleware('permission:city,update')->group(function () {
        Route::put('cities/{city}', [CityController::class, 'update']);
        Route::patch('cities/{city}', [CityController::class, 'update']);
        Route::patch('cities/{city}/toggle-status', [CityController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:city,delete')->group(function () {
        Route::delete('cities/{city}', [CityController::class, 'destroy']);
    });

    // Language routes - with permission checks
    Route::middleware('permission:language,read')->group(function () {
        Route::get('languages', [LanguageController::class, 'index']);
        Route::get('languages/{language}', [LanguageController::class, 'show']);
        Route::get('languages-statistics', [LanguageController::class, 'getStatistics']);
    });
    
    Route::middleware('permission:language,create')->group(function () {
        Route::post('languages', [LanguageController::class, 'store']);
    });
    
    Route::middleware('permission:language,update')->group(function () {
        Route::put('languages/{language}', [LanguageController::class, 'update']);
        Route::patch('languages/{language}', [LanguageController::class, 'update']);
        Route::patch('languages/{language}/set-default', [LanguageController::class, 'setDefault']);
    });
    
    Route::middleware('permission:language,delete')->group(function () {
        Route::delete('languages/{language}', [LanguageController::class, 'destroy']);
    });

    // Currency routes - with permission checks
    Route::middleware('permission:currency,read')->group(function () {
        Route::get('currencies', [CurrencyController::class, 'index']);
        Route::get('currencies/{currency}', [CurrencyController::class, 'show']);
        Route::get('currencies-statistics', [CurrencyController::class, 'getStatistics']);
    });
    
    Route::middleware('permission:currency,create')->group(function () {
        Route::post('currencies', [CurrencyController::class, 'store']);
    });
    
    Route::middleware('permission:currency,update')->group(function () {
        Route::put('currencies/{currency}', [CurrencyController::class, 'update']);
        Route::patch('currencies/{currency}', [CurrencyController::class, 'update']);
        Route::patch('currencies/{currency}/toggle-status', [CurrencyController::class, 'toggleStatus']);
    });
    
    Route::middleware('permission:currency,delete')->group(function () {
        Route::delete('currencies/{currency}', [CurrencyController::class, 'destroy']);
    });
});

// Environment-aware API info endpoint (public)
Route::get('info', function() {
    return response()->json([
        'success' => true,
        'environment' => app()->environment(),
        'app_url' => config('app.url'),
        'api_base_url' => config('app.url') . '/api',
        'request_info' => [
            'host' => request()->getHost(),
            'port' => request()->getPort(),
            'scheme' => request()->getScheme(),
            'full_url' => request()->fullUrl(),
        ],
        'cors_origins' => explode(',', env('CORS_ALLOWED_ORIGINS', '')),
        'sanctum_domains' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', '')),
    ]);
});
