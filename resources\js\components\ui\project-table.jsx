import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  MoreHorizontal, 
  Settings, 
  Plus,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';

const ProjectTable = () => {
  // Initialize activeTab from localStorage or default to 'Outline'
  const [activeTab, setActiveTab] = useState(() => {
    const savedTab = localStorage.getItem('projectTable_activeTab');
    return savedTab || 'Outline';
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const tabs = [
    { name: 'Outline', count: null },
    { name: 'Past Performance', count: 3 },
    { name: 'Key Personnel', count: 2 },
    { name: 'Focus Documents', count: null },
  ];

  // Validate saved tab exists in available tabs
  useEffect(() => {
    const savedTab = localStorage.getItem('projectTable_activeTab');
    if (savedTab) {
      const tabExists = tabs.some(tab => tab.name === savedTab);
      if (!tabExists) {
        // If saved tab doesn't exist, reset to default and clear localStorage
        setActiveTab('Outline');
        localStorage.setItem('projectTable_activeTab', 'Outline');
      }
    }
  }, []);

  const allTableData = {
    'Outline': [
      {
        id: 1,
        header: 'Cover page',
        sectionType: 'Cover page',
        status: 'In Process',
        target: 18,
        limit: 5,
        reviewer: 'Eddie Lake'
      },
      {
        id: 2,
        header: 'Table of contents',
        sectionType: 'Table of contents',
        status: 'Done',
        target: 29,
        limit: 24,
        reviewer: 'Eddie Lake'
      },
      {
        id: 3,
        header: 'Executive summary',
        sectionType: 'Narrative',
        status: 'Done',
        target: 10,
        limit: 13,
        reviewer: 'Eddie Lake'
      },
      {
        id: 4,
        header: 'Technical approach',
        sectionType: 'Narrative',
        status: 'Done',
        target: 27,
        limit: 23,
        reviewer: 'Jamik Tashpulatov'
      },
      {
        id: 5,
        header: 'Design',
        sectionType: 'Narrative',
        status: 'In Process',
        target: 2,
        limit: 16,
        reviewer: 'Jamik Tashpulatov'
      },
      {
        id: 6,
        header: 'Capabilities',
        sectionType: 'Narrative',
        status: 'In Process',
        target: 20,
        limit: 8,
        reviewer: 'Jamik Tashpulatov'
      },
      {
        id: 7,
        header: 'Integration with existing systems',
        sectionType: 'Narrative',
        status: 'In Process',
        target: 19,
        limit: 21,
        reviewer: 'Jamik Tashpulatov'
      },
      {
        id: 8,
        header: 'Innovation and Advantages',
        sectionType: 'Narrative',
        status: 'Done',
        target: 25,
        limit: 26,
        reviewer: 'Assign reviewer'
      },
      {
        id: 9,
        header: "Overview of EMR's Innovative Solutions",
        sectionType: 'Technical content',
        status: 'Done',
        target: 7,
        limit: 23,
        reviewer: 'Assign reviewer'
      },
      {
        id: 10,
        header: 'Advanced Algorithms and Machine Learning',
        sectionType: 'Narrative',
        status: 'Done',
        target: 30,
        limit: 28,
        reviewer: 'Assign reviewer'
      }
    ],
    'Past Performance': [
      {
        id: 11,
        header: 'Previous Project Alpha',
        sectionType: 'Performance data',
        status: 'Done',
        target: 15,
        limit: 12,
        reviewer: 'John Smith'
      },
      {
        id: 12,
        header: 'Client Testimonials',
        sectionType: 'Reference',
        status: 'In Process',
        target: 8,
        limit: 10,
        reviewer: 'Sarah Johnson'
      },
      {
        id: 13,
        header: 'Success Metrics',
        sectionType: 'Analytics',
        status: 'Done',
        target: 22,
        limit: 18,
        reviewer: 'Mike Davis'
      }
    ],
    'Key Personnel': [
      {
        id: 14,
        header: 'Project Manager Bio',
        sectionType: 'Personnel',
        status: 'Done',
        target: 12,
        limit: 15,
        reviewer: 'HR Team'
      },
      {
        id: 15,
        header: 'Technical Lead Profile',
        sectionType: 'Personnel',
        status: 'In Process',
        target: 10,
        limit: 12,
        reviewer: 'HR Team'
      }
    ],
    'Focus Documents': [
      {
        id: 16,
        header: 'Requirements Document',
        sectionType: 'Documentation',
        status: 'In Process',
        target: 35,
        limit: 40,
        reviewer: 'Document Team'
      },
      {
        id: 17,
        header: 'Technical Specifications',
        sectionType: 'Documentation',
        status: 'Done',
        target: 28,
        limit: 30,
        reviewer: 'Tech Team'
      },
      {
        id: 18,
        header: 'Implementation Plan',
        sectionType: 'Planning',
        status: 'In Process',
        target: 20,
        limit: 25,
        reviewer: 'Planning Team'
      }
    ]
  };

  const tableData = allTableData[activeTab] || [];

  // Debug logging
  console.log('Active Tab:', activeTab);
  console.log('Table Data Length:', tableData.length);
  console.log('Available Tabs:', Object.keys(allTableData));

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Done':
        return <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">● Done</Badge>;
      case 'In Process':
        return <Badge variant="outline" className="text-orange-600 border-orange-200">◐ In Process</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getSectionTypeBadge = (type) => {
    const baseClasses = "text-xs font-normal";
    switch (type) {
      case 'Cover page':
        return <span className={`${baseClasses} text-gray-500`}>{type}</span>;
      case 'Table of contents':
        return <span className={`${baseClasses} text-blue-600`}>{type}</span>;
      case 'Narrative':
        return <span className={`${baseClasses} text-blue-600`}>{type}</span>;
      case 'Technical content':
        return <span className={`${baseClasses} text-purple-600`}>{type}</span>;
      default:
        return <span className={`${baseClasses} text-gray-500`}>{type}</span>;
    }
  };

  const totalPages = Math.ceil(tableData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentData = tableData.slice(startIndex, endIndex);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {tabs.map((tab) => (
              <button
                key={tab.name}
                onClick={() => {
                  console.log('Tab clicked:', tab.name);
                  setActiveTab(tab.name);
                  setCurrentPage(1); // Reset to first page when switching tabs
                  // Save the selected tab to localStorage
                  localStorage.setItem('projectTable_activeTab', tab.name);
                }}
                className={`flex items-center space-x-2 pb-2 border-b-2 transition-all duration-200 cursor-pointer ${
                  activeTab === tab.name
                    ? 'border-blue-600 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <span className="text-sm font-medium">{tab.name}</span>
                {tab.count && (
                  <Badge variant="secondary" className="text-xs">
                    {tab.count}
                  </Badge>
                )}
              </button>
            ))}
          </div>
          <div className="flex items-center space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Customize Columns
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>Show/Hide Columns</DropdownMenuItem>
                <DropdownMenuItem>Reset to Default</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Section
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Debug indicator */}
        <div className="mb-4 p-2 bg-gray-100 rounded text-sm">
          <strong>Active Tab:</strong> {activeTab} | <strong>Items:</strong> {tableData.length}
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="w-12">#</TableHead>
                <TableHead>Header</TableHead>
                <TableHead>Section Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-center">Target</TableHead>
                <TableHead className="text-center">Limit</TableHead>
                <TableHead>Reviewer</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.map((row, index) => (
                <TableRow key={row.id} className="hover:bg-gray-50">
                  <TableCell className="text-gray-400 text-sm">
                    {startIndex + index + 1}
                  </TableCell>
                  <TableCell className="font-medium">{row.header}</TableCell>
                  <TableCell>{getSectionTypeBadge(row.sectionType)}</TableCell>
                  <TableCell>{getStatusBadge(row.status)}</TableCell>
                  <TableCell className="text-center">{row.target}</TableCell>
                  <TableCell className="text-center">{row.limit}</TableCell>
                  <TableCell>
                    {row.reviewer === 'Assign reviewer' ? (
                      <Button variant="ghost" size="sm" className="text-gray-500 h-auto p-0">
                        Assign reviewer ↓
                      </Button>
                    ) : (
                      <span className="text-sm">{row.reviewer}</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Edit</DropdownMenuItem>
                        <DropdownMenuItem>Duplicate</DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-gray-500">
            0 of {tableData.length} row(s) selected.
          </div>
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Rows per page</span>
              <select
                value={rowsPerPage}
                onChange={(e) => setRowsPerPage(Number(e.target.value))}
                className="border rounded px-2 py-1 text-sm"
              >
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
            </div>
            <div className="text-sm text-gray-500">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
                className="h-8 w-8 p-0"
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="h-8 w-8 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="h-8 w-8 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
                className="h-8 w-8 p-0"
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProjectTable;
