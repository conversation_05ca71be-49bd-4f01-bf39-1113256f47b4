<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LandAddress extends Model
{
    protected $fillable = [
        'land_acquisition_id',
        'plot_no',
        'road',
        'area',
        'upazila',
        'thana',
        'city',
        'district',
        'country',
        'zip_code'
    ];

    /**
     * Get the land acquisition that owns this address
     */
    public function landAcquisition(): BelongsTo
    {
        return $this->belongsTo(LandAcquisition::class);
    }
}
