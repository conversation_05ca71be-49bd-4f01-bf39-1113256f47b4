<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LandAddress extends Model
{
    protected $fillable = [
        'land_acquisition_id',
        'country_id',
        'state_id', 
        'city_id',
        'specific_address'
    ];

    /**
     * Get the land acquisition that owns this address
     */
    public function landAcquisition(): BelongsTo
    {
        return $this->belongsTo(LandAcquisition::class);
    }

    /**
     * Get the country
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the state
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Get the city
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    /**
     * Get formatted full address
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->specific_address,
            $this->city?->name,
            $this->state?->name,
            $this->country?->name
        ]);
        
        return implode(', ', $parts);
    }
}
