import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import ApiDebugPanel from '../components/ApiDebugPanel';
import * as landOwnerAPI from '../services/landOwnerAPI';

const ApiTestPage = () => {
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);

  const runApiTest = async (testName, testFunction) => {
    setLoading(true);
    try {
      console.log(`🧪 Running test: ${testName}`);
      const result = await testFunction();
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: true, data: result, error: null }
      }));
      console.log(`✅ Test ${testName} passed:`, result);
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: false, data: null, error: error.message }
      }));
      console.error(`❌ Test ${testName} failed:`, error);
    }
    setLoading(false);
  };

  const runAllTests = async () => {
    console.log('🚀 Starting comprehensive API tests...');
    
    // Test 1: Get all land owners
    await runApiTest('Get All Land Owners', () => landOwnerAPI.getAll());
    
    // Test 2: Get audit logs
    await runApiTest('Get Audit Logs', () => landOwnerAPI.getAllAudits());
    
    // Test 3: Get API info
    await runApiTest('Get API Info', () => {
      const info = landOwnerAPI.getApiInfo();
      return Promise.resolve(info);
    });

    console.log('✅ All API tests completed');
  };

  const clearResults = () => {
    setTestResults({});
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">API Testing & Debugging</h1>
        <p className="text-muted-foreground mt-2">
          Test your API connectivity across different environments
        </p>
      </div>

      {/* Quick Test Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">API Functionality Tests</h2>
            <div className="flex gap-2">
              <Button 
                onClick={runAllTests}
                disabled={loading}
                variant="default"
              >
                {loading ? 'Testing...' : 'Run All Tests'}
              </Button>
              <Button 
                onClick={clearResults}
                variant="outline"
                disabled={Object.keys(testResults).length === 0}
              >
                Clear Results
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {Object.keys(testResults).length === 0 ? (
            <Alert>
              <AlertDescription>
                Click "Run All Tests" to test your API endpoints and verify connectivity.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {Object.entries(testResults).map(([testName, result]) => (
                <div key={testName} className="border rounded p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">{testName}</h3>
                    <Badge variant={result.success ? 'default' : 'destructive'}>
                      {result.success ? 'Passed' : 'Failed'}
                    </Badge>
                  </div>
                  
                  {result.success ? (
                    <div className="text-sm text-muted-foreground">
                      <p className="text-green-600">✅ Test completed successfully</p>
                      {result.data && (
                        <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto max-h-32">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      )}
                    </div>
                  ) : (
                    <div className="text-sm">
                      <p className="text-red-600">❌ {result.error}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Debug Panel */}
      <ApiDebugPanel />

      {/* Instructions */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Environment-Aware API Instructions</h2>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium text-lg mb-2">🏠 Local Development</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Automatically detects localhost/127.0.0.1</li>
              <li>Works with XAMPP setup (detects /real-estate-management path)</li>
              <li>Works with Laravel dev server (php artisan serve)</li>
              <li>No manual configuration needed</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-lg mb-2">🌐 Production</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Automatically uses your production domain</li>
              <li>Detects HTTPS/HTTP automatically</li>
              <li>Handles Cloudways and other hosting platforms</li>
              <li>Automatic fallback if primary URL fails</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-lg mb-2">🔧 Features</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Automatic environment detection</li>
              <li>API connectivity testing and fallback</li>
              <li>Network error handling with URL switching</li>
              <li>Debug logging and monitoring</li>
              <li>No .env file changes required</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiTestPage;
